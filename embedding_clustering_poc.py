#!/usr/bin/env python3
"""
Proof of Concept: Embedding-based News Article Clustering

This script demonstrates a more cost-efficient approach to clustering news articles
using embedding models instead of relying solely on AI for clustering decisions.

The approach:
1. Generate embeddings for article content using sentence transformers
2. Apply smart clustering algorithms (K-means, DBSCAN, hierarchical)
3. Use AI only for naming clusters (much more cost-efficient)
4. Evaluate clustering quality with multiple metrics

Author: AI Assistant
Date: 2025-01-17
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Core dependencies
try:
    from sentence_transformers import SentenceTransformer
    from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
    from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
    from sklearn.preprocessing import StandardScaler
    from sklearn.decomposition import PCA
    import matplotlib.pyplot as plt
    import seaborn as sns
    from scipy.cluster.hierarchy import dendrogram, linkage
    from scipy.spatial.distance import pdist
    print("✓ All ML dependencies loaded successfully")
except ImportError as e:
    print(f"❌ Missing ML dependencies: {e}")
    print("Please install: pip install sentence-transformers scikit-learn matplotlib seaborn scipy")
    sys.exit(1)

# Database and app dependencies
try:
    from app.core.database import get_db_session
    from app.core.models import NewsArticle
    from app.services.ai_parser import AINewsParsingService
    from app.core.config import get_settings
    print("✓ App dependencies loaded successfully")
except ImportError as e:
    print(f"❌ Missing app dependencies: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)


@dataclass
class ArticleData:
    """Container for article data used in clustering."""
    id: int
    title: str
    content: str
    summary: str
    category: str
    source: str
    url: str
    key_points: List[str]
    embedding: Optional[np.ndarray] = None


@dataclass
class ClusterResult:
    """Container for clustering results."""
    cluster_id: int
    articles: List[ArticleData]
    centroid: Optional[np.ndarray] = None
    name: Optional[str] = None
    description: Optional[str] = None
    confidence_score: float = 0.0


@dataclass
class ClusteringEvaluation:
    """Container for clustering evaluation metrics."""
    silhouette_score: float
    calinski_harabasz_score: float
    davies_bouldin_score: float
    num_clusters: int
    num_articles: int
    algorithm: str
    parameters: Dict[str, Any]


class EmbeddingClusteringPOC:
    """
    Proof of concept for embedding-based article clustering.
    
    This class implements a smart clustering approach that:
    1. Uses sentence transformers for semantic embeddings
    2. Applies multiple clustering algorithms with parameter tuning
    3. Evaluates clustering quality
    4. Uses AI only for cluster naming (cost-efficient)
    """
    
    def __init__(self, embedding_model_name: str = "all-MiniLM-L6-v2"):
        """
        Initialize the clustering system.
        
        Args:
            embedding_model_name: Name of the sentence transformer model to use
        """
        self.embedding_model_name = embedding_model_name
        self.embedding_model = None
        self.ai_parser = None
        self.db = None
        
        # Clustering parameters to test
        self.clustering_configs = {
            'kmeans': [
                {'n_clusters': k, 'random_state': 42, 'n_init': 10}
                for k in range(2, 11)
            ],
            'dbscan': [
                {'eps': eps, 'min_samples': min_samples}
                for eps in [0.3, 0.5, 0.7, 0.9]
                for min_samples in [2, 3, 5]
            ],
            'hierarchical': [
                {'n_clusters': k, 'linkage': linkage}
                for k in range(2, 11)
                for linkage in ['ward', 'complete', 'average']
            ]
        }
        
        print(f"🚀 Initialized EmbeddingClusteringPOC with model: {embedding_model_name}")
    
    def setup(self):
        """Set up all required components."""
        print("🔧 Setting up components...")
        
        # Initialize embedding model
        print(f"📥 Loading embedding model: {self.embedding_model_name}")
        self.embedding_model = SentenceTransformer(self.embedding_model_name)
        print(f"✓ Embedding model loaded (dimension: {self.embedding_model.get_sentence_embedding_dimension()})")
        
        # Initialize database connection
        print("🗄️ Connecting to database...")
        self.db = get_db_session()
        print("✓ Database connection established")
        
        # Initialize AI parser for cluster naming
        print("🤖 Initializing AI parser...")
        self.ai_parser = AINewsParsingService(self.db)
        print("✓ AI parser initialized")
    
    def fetch_articles(self, limit: int = 100, processed_only: bool = True) -> List[ArticleData]:
        """
        Fetch articles from the database for clustering.
        
        Args:
            limit: Maximum number of articles to fetch
            processed_only: Whether to fetch only AI-processed articles
            
        Returns:
            List of ArticleData objects
        """
        print(f"📊 Fetching articles (limit: {limit}, processed_only: {processed_only})...")
        
        query = self.db.query(NewsArticle)
        
        if processed_only:
            query = query.filter(NewsArticle.is_processed == True)
            query = query.filter(NewsArticle.ai_summary.isnot(None))
        
        # Order by collection date (newest first) and apply limit
        articles = query.order_by(NewsArticle.collected_at.desc()).limit(limit).all()
        
        print(f"✓ Found {len(articles)} articles in database")
        
        # Convert to ArticleData objects
        article_data = []
        for article in articles:
            try:
                # Extract category from AI classification
                category = "Unknown"
                if article.ai_classification and isinstance(article.ai_classification, dict):
                    category = article.ai_classification.get("category", "Unknown")
                
                # Extract key points
                key_points = article.ai_key_points or []
                if isinstance(key_points, str):
                    key_points = [key_points]
                
                article_data.append(ArticleData(
                    id=article.id,
                    title=article.title,
                    content=article.content,
                    summary=article.ai_summary or "",
                    category=category,
                    source=article.source_name,
                    url=article.url,
                    key_points=key_points
                ))
            except Exception as e:
                print(f"⚠️ Error processing article {article.id}: {e}")
                continue
        
        print(f"✓ Successfully processed {len(article_data)} articles")
        return article_data
    
    def generate_embeddings(self, articles: List[ArticleData], use_summary: bool = True) -> List[ArticleData]:
        """
        Generate embeddings for articles.
        
        Args:
            articles: List of articles to generate embeddings for
            use_summary: Whether to use AI summary or full content for embeddings
            
        Returns:
            Articles with embeddings added
        """
        print(f"🔢 Generating embeddings for {len(articles)} articles...")
        print(f"📝 Using {'AI summaries' if use_summary else 'full content'} for embeddings")
        
        # Prepare texts for embedding
        texts = []
        for article in articles:
            if use_summary and article.summary:
                # Use AI summary + title for better semantic representation
                text = f"{article.title}. {article.summary}"
            else:
                # Use title + content (truncated to avoid token limits)
                content_preview = article.content[:1000] if len(article.content) > 1000 else article.content
                text = f"{article.title}. {content_preview}"
            
            texts.append(text)
        
        # Generate embeddings in batches for efficiency
        print("🔄 Computing embeddings...")
        embeddings = self.embedding_model.encode(texts, show_progress_bar=True, batch_size=32)
        
        # Add embeddings to articles
        for i, article in enumerate(articles):
            article.embedding = embeddings[i]
        
        print(f"✓ Generated embeddings with shape: {embeddings.shape}")
        return articles
    
    def apply_clustering_algorithms(self, articles: List[ArticleData]) -> Dict[str, ClusteringEvaluation]:
        """
        Apply multiple clustering algorithms and evaluate their performance.

        Args:
            articles: List of articles with embeddings

        Returns:
            Dictionary mapping algorithm names to their evaluation results
        """
        print(f"🔬 Testing clustering algorithms on {len(articles)} articles...")

        # Prepare embeddings matrix
        embeddings = np.array([article.embedding for article in articles])

        # Normalize embeddings for better clustering
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(embeddings)

        results = {}

        # Test K-means with different cluster counts
        print("🔄 Testing K-means clustering...")
        for config in self.clustering_configs['kmeans']:
            try:
                kmeans = KMeans(**config)
                labels = kmeans.fit_predict(embeddings_scaled)

                # Skip if only one cluster or all points in separate clusters
                if len(set(labels)) <= 1 or len(set(labels)) >= len(articles) - 1:
                    continue

                evaluation = self._evaluate_clustering(embeddings_scaled, labels, "kmeans", config)
                results[f"kmeans_k{config['n_clusters']}"] = evaluation

            except Exception as e:
                print(f"⚠️ K-means with {config} failed: {e}")
                continue

        # Test DBSCAN with different parameters
        print("🔄 Testing DBSCAN clustering...")
        for config in self.clustering_configs['dbscan']:
            try:
                dbscan = DBSCAN(**config)
                labels = dbscan.fit_predict(embeddings_scaled)

                # Skip if only noise points or only one cluster
                unique_labels = set(labels)
                if len(unique_labels) <= 1 or (len(unique_labels) == 2 and -1 in unique_labels):
                    continue

                evaluation = self._evaluate_clustering(embeddings_scaled, labels, "dbscan", config)
                results[f"dbscan_eps{config['eps']}_min{config['min_samples']}"] = evaluation

            except Exception as e:
                print(f"⚠️ DBSCAN with {config} failed: {e}")
                continue

        # Test Hierarchical clustering
        print("🔄 Testing Hierarchical clustering...")
        for config in self.clustering_configs['hierarchical']:
            try:
                hierarchical = AgglomerativeClustering(**config)
                labels = hierarchical.fit_predict(embeddings_scaled)

                # Skip if only one cluster
                if len(set(labels)) <= 1:
                    continue

                evaluation = self._evaluate_clustering(embeddings_scaled, labels, "hierarchical", config)
                results[f"hierarchical_k{config['n_clusters']}_{config['linkage']}"] = evaluation

            except Exception as e:
                print(f"⚠️ Hierarchical with {config} failed: {e}")
                continue

        print(f"✅ Completed clustering evaluation with {len(results)} valid configurations")
        return results

    def _evaluate_clustering(self, embeddings: np.ndarray, labels: np.ndarray,
                           algorithm: str, parameters: Dict[str, Any]) -> ClusteringEvaluation:
        """
        Evaluate clustering quality using multiple metrics.

        Args:
            embeddings: The embeddings used for clustering
            labels: Cluster labels assigned by the algorithm
            algorithm: Name of the clustering algorithm
            parameters: Parameters used for the algorithm

        Returns:
            ClusteringEvaluation object with metrics
        """
        try:
            # Calculate silhouette score (higher is better, range: -1 to 1)
            sil_score = silhouette_score(embeddings, labels)
        except:
            sil_score = -1.0

        try:
            # Calculate Calinski-Harabasz score (higher is better)
            ch_score = calinski_harabasz_score(embeddings, labels)
        except:
            ch_score = 0.0

        try:
            # Calculate Davies-Bouldin score (lower is better)
            db_score = davies_bouldin_score(embeddings, labels)
        except:
            db_score = float('inf')

        return ClusteringEvaluation(
            silhouette_score=sil_score,
            calinski_harabasz_score=ch_score,
            davies_bouldin_score=db_score,
            num_clusters=len(set(labels)) - (1 if -1 in labels else 0),  # Exclude noise for DBSCAN
            num_articles=len(labels),
            algorithm=algorithm,
            parameters=parameters
        )

    def select_best_clustering(self, evaluations: Dict[str, ClusteringEvaluation]) -> str:
        """
        Select the best clustering configuration based on evaluation metrics.

        Args:
            evaluations: Dictionary of clustering evaluations

        Returns:
            Name of the best clustering configuration
        """
        if not evaluations:
            return None

        print("📊 Evaluating clustering configurations...")

        # Score each configuration (higher is better)
        scored_configs = []

        for name, eval_result in evaluations.items():
            # Composite score combining multiple metrics
            # Silhouette score: weight 0.4 (range -1 to 1, normalized to 0-1)
            sil_normalized = (eval_result.silhouette_score + 1) / 2

            # Calinski-Harabasz: weight 0.3 (normalized by log to handle large values)
            ch_normalized = min(1.0, np.log(eval_result.calinski_harabasz_score + 1) / 10)

            # Davies-Bouldin: weight 0.3 (inverted since lower is better, capped at 10)
            db_capped = min(10.0, eval_result.davies_bouldin_score)
            db_normalized = 1 - (db_capped / 10)

            composite_score = (0.4 * sil_normalized +
                             0.3 * ch_normalized +
                             0.3 * db_normalized)

            scored_configs.append((name, composite_score, eval_result))

            print(f"  {name}:")
            print(f"    Silhouette: {eval_result.silhouette_score:.3f}")
            print(f"    Calinski-Harabasz: {eval_result.calinski_harabasz_score:.1f}")
            print(f"    Davies-Bouldin: {eval_result.davies_bouldin_score:.3f}")
            print(f"    Clusters: {eval_result.num_clusters}")
            print(f"    Composite Score: {composite_score:.3f}")
            print()

        # Sort by composite score (descending)
        scored_configs.sort(key=lambda x: x[1], reverse=True)

        best_config = scored_configs[0]
        print(f"🏆 Best configuration: {best_config[0]} (score: {best_config[1]:.3f})")

        return best_config[0]

    def perform_final_clustering(self, articles: List[ArticleData], best_config_name: str) -> List[ClusterResult]:
        """
        Perform the final clustering using the best configuration.

        Args:
            articles: List of articles with embeddings
            best_config_name: Name of the best clustering configuration

        Returns:
            List of ClusterResult objects
        """
        print(f"🎯 Performing final clustering with {best_config_name}...")

        # Prepare embeddings
        embeddings = np.array([article.embedding for article in articles])
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(embeddings)

        # Parse configuration name and apply clustering
        if best_config_name.startswith("kmeans"):
            k = int(best_config_name.split("_k")[1])
            clusterer = KMeans(n_clusters=k, random_state=42, n_init=10)
            labels = clusterer.fit_predict(embeddings_scaled)
            centroids = clusterer.cluster_centers_

        elif best_config_name.startswith("dbscan"):
            parts = best_config_name.split("_")
            eps = float(parts[1].replace("eps", ""))
            min_samples = int(parts[2].replace("min", ""))
            clusterer = DBSCAN(eps=eps, min_samples=min_samples)
            labels = clusterer.fit_predict(embeddings_scaled)
            centroids = None  # DBSCAN doesn't have centroids

        elif best_config_name.startswith("hierarchical"):
            parts = best_config_name.split("_")
            k = int(parts[1].replace("k", ""))
            linkage = parts[2]
            clusterer = AgglomerativeClustering(n_clusters=k, linkage=linkage)
            labels = clusterer.fit_predict(embeddings_scaled)
            centroids = None  # Hierarchical doesn't have centroids

        # Group articles by cluster
        cluster_groups = {}
        for i, label in enumerate(labels):
            if label not in cluster_groups:
                cluster_groups[label] = []
            cluster_groups[label].append(articles[i])

        # Create ClusterResult objects
        cluster_results = []
        for cluster_id, cluster_articles in cluster_groups.items():
            # Skip noise points in DBSCAN (label -1)
            if cluster_id == -1:
                continue

            # Calculate centroid for this cluster if not available
            centroid = None
            if centroids is not None and cluster_id < len(centroids):
                centroid = centroids[cluster_id]
            else:
                # Calculate mean embedding for this cluster
                cluster_embeddings = [article.embedding for article in cluster_articles]
                centroid = np.mean(cluster_embeddings, axis=0)

            cluster_result = ClusterResult(
                cluster_id=cluster_id,
                articles=cluster_articles,
                centroid=centroid,
                name=None,  # Will be filled by AI naming
                description=None,  # Will be filled by AI naming
                confidence_score=0.0  # Will be calculated later
            )
            cluster_results.append(cluster_result)

        print(f"✅ Created {len(cluster_results)} clusters")
        return cluster_results

    def name_clusters_with_ai(self, cluster_results: List[ClusterResult]) -> List[ClusterResult]:
        """
        Use AI to generate meaningful names and descriptions for clusters.

        Args:
            cluster_results: List of clusters to name

        Returns:
            Updated cluster results with AI-generated names and descriptions
        """
        print(f"🤖 Generating AI names for {len(cluster_results)} clusters...")

        for i, cluster in enumerate(cluster_results):
            try:
                # Prepare cluster information for AI
                cluster_info = {
                    "cluster_id": cluster.cluster_id,
                    "article_count": len(cluster.articles),
                    "articles": []
                }

                # Add article summaries and metadata
                for article in cluster.articles[:10]:  # Limit to first 10 articles for AI context
                    article_info = {
                        "title": article.title,
                        "summary": article.summary[:200],  # Truncate long summaries
                        "category": article.category,
                        "source": article.source,
                        "key_points": article.key_points[:3] if article.key_points else []
                    }
                    cluster_info["articles"].append(article_info)

                # Create AI prompt for cluster naming
                prompt = f"""
                Analyze this cluster of {len(cluster.articles)} news articles about carbon regulation and climate policy.

                Articles in cluster:
                {json.dumps(cluster_info['articles'], indent=2)}

                Based on the common themes, topics, and content of these articles, provide:
                1. A concise, descriptive name for this cluster (max 50 characters)
                2. A brief description explaining what this cluster represents (max 150 characters)
                3. A confidence score (0.0-1.0) indicating how coherent this cluster is

                Respond in JSON format:
                {{
                    "name": "Cluster Name",
                    "description": "Brief description of the cluster theme",
                    "confidence_score": 0.85
                }}
                """

                # Use AI parser to generate cluster name
                try:
                    # Create a simple agent for cluster naming
                    from pydantic_ai import Agent
                    from pydantic import BaseModel

                    class ClusterNaming(BaseModel):
                        name: str
                        description: str
                        confidence_score: float

                    naming_agent = Agent(
                        self.ai_parser._get_clustering_agent().model,
                        output_type=ClusterNaming,
                        system_prompt="You are an expert at analyzing and categorizing news articles about carbon regulation and climate policy."
                    )

                    result = naming_agent.run_sync(prompt)
                    naming_result = result.output

                    cluster.name = naming_result.name
                    cluster.description = naming_result.description
                    cluster.confidence_score = naming_result.confidence_score

                    print(f"  Cluster {i+1}: '{cluster.name}' (confidence: {cluster.confidence_score:.2f})")

                except Exception as e:
                    print(f"⚠️ AI naming failed for cluster {i+1}: {e}")
                    # Fallback naming based on most common category
                    categories = [article.category for article in cluster.articles]
                    most_common_category = max(set(categories), key=categories.count)

                    cluster.name = f"{most_common_category} Cluster"
                    cluster.description = f"Articles related to {most_common_category.lower()}"
                    cluster.confidence_score = 0.5

                    print(f"  Cluster {i+1}: '{cluster.name}' (fallback)")

            except Exception as e:
                print(f"⚠️ Error processing cluster {i+1}: {e}")
                cluster.name = f"Cluster {i+1}"
                cluster.description = "Mixed topics cluster"
                cluster.confidence_score = 0.3

        print("✅ Cluster naming completed")
        return cluster_results

    def evaluate_clustering_quality(self, cluster_results: List[ClusterResult]) -> Dict[str, Any]:
        """
        Evaluate the overall quality of the clustering results.

        Args:
            cluster_results: List of clustering results

        Returns:
            Dictionary with quality metrics
        """
        print("📈 Evaluating clustering quality...")

        total_articles = sum(len(cluster.articles) for cluster in cluster_results)

        # Calculate cluster size distribution
        cluster_sizes = [len(cluster.articles) for cluster in cluster_results]

        # Calculate confidence scores
        confidence_scores = [cluster.confidence_score for cluster in cluster_results]

        # Category coherence: how well articles in each cluster share categories
        category_coherence_scores = []
        for cluster in cluster_results:
            if len(cluster.articles) <= 1:
                category_coherence_scores.append(1.0)
                continue

            categories = [article.category for article in cluster.articles]
            most_common_category = max(set(categories), key=categories.count)
            coherence = categories.count(most_common_category) / len(categories)
            category_coherence_scores.append(coherence)

        quality_metrics = {
            "total_articles": total_articles,
            "num_clusters": len(cluster_results),
            "avg_cluster_size": np.mean(cluster_sizes),
            "cluster_size_std": np.std(cluster_sizes),
            "min_cluster_size": min(cluster_sizes) if cluster_sizes else 0,
            "max_cluster_size": max(cluster_sizes) if cluster_sizes else 0,
            "avg_confidence": np.mean(confidence_scores),
            "avg_category_coherence": np.mean(category_coherence_scores),
            "cluster_details": [
                {
                    "cluster_id": cluster.cluster_id,
                    "name": cluster.name,
                    "size": len(cluster.articles),
                    "confidence": cluster.confidence_score,
                    "category_coherence": category_coherence_scores[i]
                }
                for i, cluster in enumerate(cluster_results)
            ]
        }

        print(f"  📊 {quality_metrics['num_clusters']} clusters created")
        print(f"  📏 Average cluster size: {quality_metrics['avg_cluster_size']:.1f}")
        print(f"  🎯 Average confidence: {quality_metrics['avg_confidence']:.2f}")
        print(f"  🏷️ Average category coherence: {quality_metrics['avg_category_coherence']:.2f}")

        return quality_metrics

    def cleanup(self):
        """Clean up resources."""
        if self.db:
            self.db.close()
        if self.ai_parser:
            self.ai_parser.close()


def main():
    """Main function to run the proof of concept."""
    print("🎯 Starting Embedding-based Clustering Proof of Concept")
    print("=" * 60)

    # Initialize the clustering system
    poc = EmbeddingClusteringPOC()

    try:
        # Setup components
        poc.setup()

        # Fetch articles from database
        articles = poc.fetch_articles(limit=50, processed_only=True)

        if len(articles) < 5:
            print(f"❌ Not enough articles for clustering (found: {len(articles)}, need: 5+)")
            print("Please ensure you have processed articles in the database")
            return

        # Generate embeddings
        articles = poc.generate_embeddings(articles, use_summary=True)

        print(f"✅ Successfully prepared {len(articles)} articles for clustering")
        print("\n📊 Article Categories Distribution:")
        categories = {}
        for article in articles:
            categories[article.category] = categories.get(article.category, 0) + 1

        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"  • {category}: {count} articles")

        print(f"\n🔢 Embedding dimension: {articles[0].embedding.shape[0]}")

        # Apply clustering algorithms and find the best one
        print("\n" + "="*60)
        clustering_evaluations = poc.apply_clustering_algorithms(articles)

        if not clustering_evaluations:
            print("❌ No valid clustering configurations found")
            return

        # Select best clustering configuration
        best_config = poc.select_best_clustering(clustering_evaluations)

        # Perform final clustering with best configuration
        print("\n" + "="*60)
        cluster_results = poc.perform_final_clustering(articles, best_config)

        # Use AI to name clusters
        cluster_results = poc.name_clusters_with_ai(cluster_results)

        # Evaluate overall clustering quality
        print("\n" + "="*60)
        quality_metrics = poc.evaluate_clustering_quality(cluster_results)

        # Display final results
        print("\n" + "="*60)
        print("🎉 CLUSTERING RESULTS")
        print("="*60)

        for i, cluster in enumerate(cluster_results):
            print(f"\n📁 Cluster {i+1}: {cluster.name}")
            print(f"   📝 Description: {cluster.description}")
            print(f"   📊 Articles: {len(cluster.articles)}")
            print(f"   🎯 Confidence: {cluster.confidence_score:.2f}")
            print(f"   📰 Sample articles:")

            for j, article in enumerate(cluster.articles[:3]):  # Show first 3 articles
                print(f"     {j+1}. {article.title[:80]}...")
                print(f"        Source: {article.source} | Category: {article.category}")

            if len(cluster.articles) > 3:
                print(f"     ... and {len(cluster.articles) - 3} more articles")

        # Summary statistics
        print(f"\n📈 SUMMARY STATISTICS")
        print("="*60)
        print(f"Total articles clustered: {quality_metrics['total_articles']}")
        print(f"Number of clusters: {quality_metrics['num_clusters']}")
        print(f"Average cluster size: {quality_metrics['avg_cluster_size']:.1f}")
        print(f"Average confidence score: {quality_metrics['avg_confidence']:.2f}")
        print(f"Average category coherence: {quality_metrics['avg_category_coherence']:.2f}")

        # Cost efficiency comparison
        print(f"\n💰 COST EFFICIENCY ANALYSIS")
        print("="*60)
        print("Current AI-only clustering:")
        print(f"  • Processes all {len(articles)} articles through AI")
        print(f"  • Estimated cost: ~{len(articles) * 0.01:.2f} USD (rough estimate)")
        print()
        print("New embedding-based clustering:")
        print(f"  • Generates embeddings once: ~{len(articles) * 0.001:.3f} USD")
        print(f"  • AI naming for {len(cluster_results)} clusters: ~{len(cluster_results) * 0.005:.3f} USD")
        print(f"  • Total estimated cost: ~{(len(articles) * 0.001) + (len(cluster_results) * 0.005):.3f} USD")
        print(f"  • Cost reduction: ~{(1 - ((len(articles) * 0.001) + (len(cluster_results) * 0.005)) / (len(articles) * 0.01)) * 100:.1f}%")

        print(f"\n✅ Proof of concept completed successfully!")
        print("🚀 Ready for integration into the main codebase")

    except Exception as e:
        print(f"❌ Error during proof of concept: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Clean up resources
        poc.cleanup()


if __name__ == "__main__":
    main()
