services:
  # Main application service
  app:
    build: .
    container_name: carbon-regulation-news
    ports:
      - "8000:8000"
    environment:
      # API Keys (required - set these in .env file)
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}

      # Optional environment overrides (most settings are in config.yaml)
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}

    volumes:
      # Persist database and logs
      - ./data:/app/data
      - ./logs:/app/logs

    restart: unless-stopped

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # Ensure data and logs directories exist
    depends_on: []
