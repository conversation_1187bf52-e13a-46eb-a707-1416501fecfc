"""
Smart news collector with AI-driven per-source instructions.
"""

import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from urllib.parse import urljoin, urlparse

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider
from pydantic_ai.settings import ModelSettings
from tavily import TavilyClient

from ..core.config import get_settings
from ..core.logging import LoggerMixin
from ..models import (
    SmartSource, SmartCollectionPlan, SmartCollectionResult, ToolExecution,
    ToolType, WebSearchResult, ContentExtractionResult, URLExtractionResult,
    RSSParseResult, ArticleFilterResult, CollectedArticle
)

# Apply feedparser patches before importing feedparser
from ..utils.feedparser_patch import apply_feedparser_patches
apply_feedparser_patches()
import feedparser


class SmartCollectorTools:
    """Tools available to the smart collector agent."""

    def __init__(self, tavily_client: Tavi<PERSON><PERSON><PERSON>, logger):
        self.tavily_client = tavily_client
        self.logger = logger

    def search_web(self, query: str, time_range: str = "day", max_results: int = 10, **kwargs) -> WebSearchResult:
        """Search the web for news articles."""
        try:
            search_response = self.tavily_client.search(
                query=query,
                topic="news",
                search_depth="basic",
                time_range=time_range,
                max_results=max_results,
                **kwargs
            )

            results = search_response.get('results', [])
            return WebSearchResult(
                query=query,
                results=results,
                total_results=len(results)
            )
        except Exception as e:
            self.logger.error(f"Web search failed: {e}")
            return WebSearchResult(query=query, results=[], total_results=0)

    def extract_content(self, url: str) -> ContentExtractionResult:
        """Extract content from a URL."""
        try:
            extract_response = self.tavily_client.extract(urls=[url])

            if extract_response and extract_response.get('results'):
                result = extract_response['results'][0]
                content = result.get('content', '')
                title = self._extract_title_from_content(content)

                return ContentExtractionResult(
                    url=url,
                    title=title,
                    content=content,
                    metadata=result
                )
            else:
                return ContentExtractionResult(url=url, title=None, content="", metadata={})

        except Exception as e:
            self.logger.error(f"Content extraction failed for {url}: {e}")
            return ContentExtractionResult(url=url, title=None, content="", metadata={})

    def extract_urls(self, source_url: str, content: str = None) -> URLExtractionResult:
        """Extract article URLs from a web page."""
        try:
            if content is None:
                # Extract content first
                extract_result = self.extract_content(source_url)
                content = extract_result.content

            # Use regex patterns to extract URLs
            urls = self._extract_urls_with_patterns(content, source_url)

            return URLExtractionResult(
                source_url=source_url,
                extracted_urls=urls,
                total_urls=len(urls)
            )

        except Exception as e:
            self.logger.error(f"URL extraction failed for {source_url}: {e}")
            return URLExtractionResult(source_url=source_url, extracted_urls=[], total_urls=0)

    def parse_rss(self, feed_url: str, max_entries: int = 20) -> RSSParseResult:
        """Parse an RSS feed."""
        try:
            feed = feedparser.parse(feed_url)

            feed_title = None
            if hasattr(feed, 'feed') and hasattr(feed.feed, 'title'):
                feed_title = feed.feed.title

            entries = []
            if hasattr(feed, 'entries'):
                for entry in feed.entries[:max_entries]:
                    entry_data = {
                        'title': getattr(entry, 'title', 'Unknown Title'),
                        'link': getattr(entry, 'link', ''),
                        'summary': getattr(entry, 'summary', getattr(entry, 'description', '')),
                        'published': getattr(entry, 'published', ''),
                        'published_parsed': getattr(entry, 'published_parsed', None)
                    }
                    entries.append(entry_data)

            return RSSParseResult(
                feed_url=feed_url,
                feed_title=feed_title,
                entries=entries,
                total_entries=len(entries)
            )

        except Exception as e:
            self.logger.error(f"RSS parsing failed for {feed_url}: {e}")
            return RSSParseResult(feed_url=feed_url, feed_title=None, entries=[], total_entries=0)

    def filter_articles(self, articles: List[Dict[str, Any]], criteria: str) -> ArticleFilterResult:
        """Filter articles based on criteria."""
        try:
            # Simple filtering based on keywords in criteria
            keywords = criteria.lower().split()
            filtered = []

            for article in articles:
                title = article.get('title', '').lower()
                content = article.get('content', '').lower()
                summary = article.get('summary', '').lower()

                # Check if any keyword is in title, content, or summary
                if any(keyword in title or keyword in content or keyword in summary for keyword in keywords):
                    filtered.append(article)

            return ArticleFilterResult(
                original_count=len(articles),
                filtered_count=len(filtered),
                filter_criteria=criteria,
                filtered_articles=filtered
            )

        except Exception as e:
            self.logger.error(f"Article filtering failed: {e}")
            return ArticleFilterResult(
                original_count=len(articles),
                filtered_count=0,
                filter_criteria=criteria,
                filtered_articles=[]
            )

    def _extract_title_from_content(self, content: str) -> Optional[str]:
        """Extract title from content."""
        lines = content.split('\n')
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if line and len(line) > 10 and len(line) < 200:
                # Skip common non-title patterns
                if not any(skip in line.lower() for skip in ['subscribe', 'menu', 'search', 'login', 'advertisement']):
                    return line
        return None

    def _extract_urls_with_patterns(self, content: str, base_url: str) -> List[str]:
        """Extract URLs using regex patterns."""
        import re

        url_patterns = [
            r'href=["\']([^"\']+/\d{4}/\d{2}/\d{2}/[^"\']+)["\']',  # Date-based URLs
            r'href=["\']([^"\']+/article/[^"\']+)["\']',             # Article paths
            r'href=["\']([^"\']+/news/[^"\']+)["\']',                # News paths
            r'href=["\']([^"\']+/sustainability/[^"\']+)["\']',      # Sustainability paths
        ]

        urls = []
        for pattern in url_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if not match.startswith('http'):
                    match = urljoin(base_url, match)
                urls.append(match)

        # Remove duplicates and return first 10
        return list(dict.fromkeys(urls))[:10]


class SmartCollectorAgent(LoggerMixin):
    """AI agent for smart news collection with per-source instructions."""

    def __init__(self, tavily_client: TavilyClient):
        """Initialize the smart collector agent."""
        self.settings = get_settings()
        self.tavily_client = tavily_client
        self.tools = SmartCollectorTools(tavily_client, self.logger)
        self.planning_agent = self._init_planning_agent()

        self.log_method_call("__init__")

    def _init_planning_agent(self) -> Agent:
        """Initialize the AI agent for planning collection strategies."""
        api_key = self.settings.openrouter_api_key
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY is required for smart collection")

        model = OpenAIChatModel(
            self.settings.news_collector.smart_agent_model,
            provider=OpenRouterProvider(api_key=api_key),
            settings=ModelSettings(
                temperature=self.settings.news_collector.smart_agent_temperature,
            ),
        )

        system_prompt = """You are an expert news collection agent that creates execution plans for gathering carbon regulation and climate policy news from various sources.

Your task is to analyze the source configuration and instructions, then create a detailed plan for collecting relevant articles.

Available tools and their REQUIRED parameters:
- search_web: REQUIRES "query" (string), optional: "time_range" (day/week/month), "max_results" (number)
- extract_content: REQUIRES "url" (string)
- extract_urls: REQUIRES "source_url" (string)
- parse_rss: REQUIRES "feed_url" (string), optional: "max_entries" (number)
- filter_articles: REQUIRES "articles" (list) and "criteria" (string)

CRITICAL: You MUST provide tool_parameters as a dictionary where each key matches a tool name exactly, and each value contains ALL required parameters:

Example format:
{
  "search_web": {"query": "carbon regulations EU", "time_range": "day", "max_results": 10},
  "extract_content": {"url": "https://example.com/article"},
  "parse_rss": {"feed_url": "https://example.com/rss", "max_entries": 20}
}

For each source, you should:
1. Understand the source type and instructions
2. Choose the most appropriate tools in the right order
3. Provide ALL required parameters for each tool
4. Explain your reasoning

Return a structured plan with the tools to use and their complete parameters."""

        self.logger.info("Initializing smart collection planning agent")
        return Agent(
            model,
            output_type=SmartCollectionPlan,
            system_prompt=system_prompt,
        )

    def collect_from_smart_source(self, source: SmartSource) -> SmartCollectionResult:
        """Collect articles from a smart source using AI-driven instructions."""
        self.log_method_call("collect_from_smart_source", source_name=source.name)
        start_time = time.time()

        try:
            # Generate collection plan
            plan = self._generate_collection_plan(source)

            # Execute the plan
            tool_executions = []
            articles = []
            intermediate_results = {}

            for tool_type in plan.tools_to_use:
                # Get parameters and inject intermediate results if needed
                params = plan.tool_parameters.get(tool_type.value, {}).copy()

                # Handle chained operations
                if tool_type == ToolType.FILTER_ARTICLES and 'articles' not in params:
                    # Use articles from previous operations
                    params['articles'] = self._prepare_articles_for_filtering(tool_executions)
                elif tool_type == ToolType.EXTRACT_CONTENT and 'url' not in params:
                    # Use URLs from previous extract_urls operation
                    urls = self._extract_urls_from_previous_results(tool_executions)
                    if urls:
                        # Extract content from multiple URLs
                        for url in urls[:5]:  # Limit to first 5 URLs to avoid too many requests
                            url_params = params.copy()
                            url_params['url'] = url
                            execution = self._execute_tool(tool_type, url_params)
                            tool_executions.append(execution)

                            if execution.success:
                                # Store intermediate results for chaining
                                intermediate_results[f"{tool_type.value}_{len(tool_executions)}"] = execution.result

                                # Convert tool results to articles
                                tool_articles = self._convert_tool_result_to_articles(execution, source)
                                articles.extend(tool_articles)
                        continue  # Skip the normal execution below

                execution = self._execute_tool(tool_type, params)
                tool_executions.append(execution)

                if execution.success:
                    # Store intermediate results for chaining
                    intermediate_results[tool_type.value] = execution.result

                    # Convert tool results to articles
                    tool_articles = self._convert_tool_result_to_articles(execution, source)
                    articles.extend(tool_articles)

            # Apply source-specific limits
            max_articles = source.max_articles or self.settings.news_collector.max_articles_per_source
            articles = articles[:max_articles]

            total_time = time.time() - start_time

            result = SmartCollectionResult(
                source_name=source.name,
                plan=plan,
                tool_executions=tool_executions,
                articles_found=len(articles),
                success=True,
                total_time=total_time
            )

            self.logger.info(
                "Smart collection completed",
                source_name=source.name,
                articles_found=len(articles),
                total_time=total_time
            )

            return result

        except Exception as e:
            total_time = time.time() - start_time
            self.log_error(e, {"source_name": source.name})

            return SmartCollectionResult(
                source_name=source.name,
                plan=SmartCollectionPlan(
                    source_name=source.name,
                    reasoning="Failed to generate plan",
                    tools_to_use=[],
                    tool_parameters={},
                    expected_outcome="Error occurred"
                ),
                tool_executions=[],
                articles_found=0,
                success=False,
                error=str(e),
                total_time=total_time
            )

    def _generate_collection_plan(self, source: SmartSource) -> SmartCollectionPlan:
        """Generate a collection plan for the source using AI."""
        prompt = f"""
Create a collection plan for this news source:

Source Name: {source.name}
Source Type: {source.type.value}
URL: {source.url or 'N/A'}
Query: {source.query or 'N/A'}
Available Tools: {[tool.value for tool in source.available_tools]}

Instructions: {source.instructions}

Additional Parameters: {source.parameters}

IMPORTANT: You must provide tool_parameters as a dictionary where each key is a tool name and the value contains the required parameters:

For search_web: {{"search_web": {{"query": "your search query", "time_range": "day", "max_results": 10}}}}
For extract_content: {{"extract_content": {{"url": "https://example.com"}}}}
For extract_urls: {{"extract_urls": {{"source_url": "https://example.com"}}}}
For parse_rss: {{"parse_rss": {{"feed_url": "https://example.com/rss", "max_entries": 20}}}}
For filter_articles: {{"filter_articles": {{"articles": [], "criteria": "carbon climate policy"}}}}

Create a plan that follows the instructions and uses the available tools effectively with proper parameters.
"""

        try:
            result = self.planning_agent.run_sync(prompt)
            plan = result.output

            # Validate and fix tool parameters
            plan = self._validate_and_fix_plan(plan, source)
            return plan
        except Exception as e:
            self.logger.warning("AI planning failed, using fallback plan", error=str(e))
            return self._create_fallback_plan(source)

    def _validate_and_fix_plan(self, plan: SmartCollectionPlan, source: SmartSource) -> SmartCollectionPlan:
        """Validate and fix tool parameters in the plan."""
        fixed_params = {}

        for tool_type in plan.tools_to_use:
            tool_name = tool_type.value
            current_params = plan.tool_parameters.get(tool_name, {})

            # Fix parameters based on tool type and source information
            if tool_type == ToolType.SEARCH_WEB:
                fixed_params[tool_name] = {
                    "query": current_params.get("query") or source.query or "carbon regulation climate policy",
                    "time_range": current_params.get("time_range") or source.time_range or "day",
                    "max_results": current_params.get("max_results") or source.max_articles or 10
                }
            elif tool_type == ToolType.EXTRACT_CONTENT:
                fixed_params[tool_name] = {
                    "url": current_params.get("url") or source.url or ""
                }
            elif tool_type == ToolType.EXTRACT_URLS:
                fixed_params[tool_name] = {
                    "source_url": current_params.get("source_url") or source.url or ""
                }
            elif tool_type == ToolType.PARSE_RSS:
                fixed_params[tool_name] = {
                    "feed_url": current_params.get("feed_url") or source.url or "",
                    "max_entries": current_params.get("max_entries") or source.max_articles or 20
                }
            elif tool_type == ToolType.FILTER_ARTICLES:
                fixed_params[tool_name] = {
                    "articles": current_params.get("articles", []),
                    "criteria": current_params.get("criteria") or "carbon climate policy regulation"
                }

        # Create new plan with fixed parameters
        return SmartCollectionPlan(
            source_name=plan.source_name,
            reasoning=plan.reasoning,
            tools_to_use=plan.tools_to_use,
            tool_parameters=fixed_params,
            expected_outcome=plan.expected_outcome
        )

    def _create_fallback_plan(self, source: SmartSource) -> SmartCollectionPlan:
        """Create a fallback plan when AI planning fails."""
        if source.type.value == "web_search" and source.query:
            tools = [ToolType.SEARCH_WEB]
            params = {"search_web": {"query": source.query}}
        elif source.type.value == "rss_feed" and source.url:
            tools = [ToolType.PARSE_RSS]
            params = {"parse_rss": {"feed_url": source.url}}
        elif source.type.value == "specific_url" and source.url:
            tools = [ToolType.EXTRACT_URLS, ToolType.EXTRACT_CONTENT]
            params = {
                "extract_urls": {"source_url": source.url},
                "extract_content": {"url": source.url}
            }
        else:
            tools = [ToolType.SEARCH_WEB]
            params = {"search_web": {"query": "carbon regulation climate policy"}}

        return SmartCollectionPlan(
            source_name=source.name,
            reasoning="Fallback plan due to AI planning failure",
            tools_to_use=tools,
            tool_parameters=params,
            expected_outcome="Basic collection using fallback strategy"
        )

    def _execute_tool(self, tool_type: ToolType, parameters: Dict[str, Any]) -> ToolExecution:
        """Execute a specific tool with given parameters."""
        start_time = time.time()

        try:
            # Log the tool execution attempt
            self.logger.debug(f"Executing tool: {tool_type.value}", parameters=parameters)

            # Validate required parameters before execution
            self._validate_tool_parameters(tool_type, parameters)

            if tool_type == ToolType.SEARCH_WEB:
                result = self.tools.search_web(**parameters)
            elif tool_type == ToolType.EXTRACT_CONTENT:
                result = self.tools.extract_content(**parameters)
            elif tool_type == ToolType.EXTRACT_URLS:
                result = self.tools.extract_urls(**parameters)
            elif tool_type == ToolType.PARSE_RSS:
                result = self.tools.parse_rss(**parameters)
            elif tool_type == ToolType.FILTER_ARTICLES:
                result = self.tools.filter_articles(**parameters)
            else:
                raise ValueError(f"Unknown tool type: {tool_type}")

            execution_time = time.time() - start_time
            self.logger.debug(f"Tool execution successful: {tool_type.value}", execution_time=execution_time)

            return ToolExecution(
                tool_name=tool_type.value,
                tool_type=tool_type,
                success=True,
                result=result,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Tool execution failed: {tool_type.value}", error=str(e), parameters=parameters)

            return ToolExecution(
                tool_name=tool_type.value,
                tool_type=tool_type,
                success=False,
                result=None,
                error=str(e),
                execution_time=execution_time
            )

    def _validate_tool_parameters(self, tool_type: ToolType, parameters: Dict[str, Any]) -> None:
        """Validate that required parameters are present for each tool."""
        required_params = {
            ToolType.SEARCH_WEB: ['query'],
            ToolType.EXTRACT_CONTENT: ['url'],
            ToolType.EXTRACT_URLS: ['source_url'],
            ToolType.PARSE_RSS: ['feed_url'],
            ToolType.FILTER_ARTICLES: ['articles', 'criteria']
        }

        required = required_params.get(tool_type, [])
        missing = [param for param in required if param not in parameters or not parameters[param]]

        if missing:
            raise ValueError(f"Missing required parameters for {tool_type.value}: {missing}")

        # Additional validation
        if tool_type == ToolType.EXTRACT_CONTENT and not parameters.get('url'):
            raise ValueError("URL parameter cannot be empty for extract_content")
        elif tool_type == ToolType.PARSE_RSS and not parameters.get('feed_url'):
            raise ValueError("feed_url parameter cannot be empty for parse_rss")
        elif tool_type == ToolType.SEARCH_WEB and not parameters.get('query'):
            raise ValueError("query parameter cannot be empty for search_web")

    def _prepare_articles_for_filtering(self, tool_executions: List[ToolExecution]) -> List[Dict[str, Any]]:
        """Prepare articles from previous tool executions for filtering."""
        articles = []

        for execution in tool_executions:
            if not execution.success:
                continue

            if execution.tool_type == ToolType.SEARCH_WEB and isinstance(execution.result, WebSearchResult):
                for result in execution.result.results:
                    articles.append({
                        'title': result.get('title', ''),
                        'content': result.get('content', ''),
                        'summary': result.get('content', '')[:200],  # First 200 chars as summary
                        'url': result.get('url', '')
                    })
            elif execution.tool_type == ToolType.PARSE_RSS and isinstance(execution.result, RSSParseResult):
                for entry in execution.result.entries:
                    articles.append({
                        'title': entry.get('title', ''),
                        'content': entry.get('summary', ''),
                        'summary': entry.get('summary', ''),
                        'url': entry.get('link', '')
                    })

        return articles

    def _extract_urls_from_previous_results(self, tool_executions: List[ToolExecution]) -> List[str]:
        """Extract URLs from previous tool executions."""
        urls = []

        for execution in tool_executions:
            if not execution.success:
                continue

            if execution.tool_type == ToolType.EXTRACT_URLS and isinstance(execution.result, URLExtractionResult):
                urls.extend(execution.result.urls)
            elif execution.tool_type == ToolType.SEARCH_WEB and isinstance(execution.result, WebSearchResult):
                for result in execution.result.results:
                    if result.get('url'):
                        urls.append(result['url'])
            elif execution.tool_type == ToolType.PARSE_RSS and isinstance(execution.result, RSSParseResult):
                for entry in execution.result.entries:
                    if entry.get('link'):
                        urls.append(entry['link'])

        return urls

    def _convert_tool_result_to_articles(self, execution: ToolExecution, source: SmartSource) -> List[CollectedArticle]:
        """Convert tool execution results to CollectedArticle objects."""
        articles = []

        try:
            if execution.tool_type == ToolType.SEARCH_WEB and isinstance(execution.result, WebSearchResult):
                for result in execution.result.results:
                    article = CollectedArticle(
                        title=result.get('title', 'Unknown Title'),
                        url=result.get('url', ''),
                        content=result.get('content', ''),
                        source_name=source.name
                    )
                    articles.append(article)

            elif execution.tool_type == ToolType.EXTRACT_CONTENT and isinstance(execution.result, ContentExtractionResult):
                if execution.result.content:
                    article = CollectedArticle(
                        title=execution.result.title or 'Unknown Title',
                        url=execution.result.url,
                        content=execution.result.content,
                        source_name=source.name
                    )
                    articles.append(article)

            elif execution.tool_type == ToolType.PARSE_RSS and isinstance(execution.result, RSSParseResult):
                for entry in execution.result.entries:
                    # Extract content from each RSS entry URL if available
                    content = entry.get('summary', '')
                    if entry.get('link'):
                        try:
                            extract_result = self.tools.extract_content(entry['link'])
                            if extract_result.content:
                                content = extract_result.content
                        except:
                            pass  # Use summary if content extraction fails

                    article = CollectedArticle(
                        title=entry.get('title', 'Unknown Title'),
                        url=entry.get('link', ''),
                        content=content,
                        source_name=source.name
                    )
                    articles.append(article)

            elif execution.tool_type == ToolType.FILTER_ARTICLES and isinstance(execution.result, ArticleFilterResult):
                for article_data in execution.result.filtered_articles:
                    article = CollectedArticle(
                        title=article_data.get('title', 'Unknown Title'),
                        url=article_data.get('url', ''),
                        content=article_data.get('content', ''),
                        source_name=source.name
                    )
                    articles.append(article)

        except Exception as e:
            self.logger.error("Failed to convert tool result to articles", error=str(e))

        return articles