"""
Embedding-based clustering service for news articles.

This service provides cost-efficient clustering using semantic embeddings
instead of AI-only clustering, while maintaining compatibility with the
existing AI parser interface.
"""

import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

try:
    from sentence_transformers import SentenceTransformer
    from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
    from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
    from sklearn.preprocessing import StandardScaler
    CLUSTERING_AVAILABLE = True
except ImportError:
    CLUSTERING_AVAILABLE = False

from ..models import ArticleClustering, ArticleCluster
from ..core.logging import get_logger


@dataclass
class ClusteringEvaluation:
    """Evaluation metrics for clustering quality."""
    silhouette_score: float
    calinski_harabasz_score: float
    davies_bouldin_score: float
    num_clusters: int
    num_articles: int
    algorithm: str
    parameters: Dict[str, Any]


class EmbeddingClusteringService:
    """
    Service for embedding-based article clustering.
    
    This service provides a cost-efficient alternative to AI-only clustering
    by using semantic embeddings and machine learning clustering algorithms.
    """
    
    def __init__(self, embedding_model_name: str = "all-MiniLM-L6-v2"):
        """
        Initialize the embedding clustering service.
        
        Args:
            embedding_model_name: Name of the sentence transformer model
        """
        self.logger = get_logger(__name__)
        self.embedding_model_name = embedding_model_name
        self.embedding_model = None
        
        # Check if clustering dependencies are available
        if not CLUSTERING_AVAILABLE:
            self.logger.warning(
                "Clustering dependencies not available. "
                "Install sentence-transformers and scikit-learn for embedding clustering."
            )
        
        # Clustering configurations for automatic parameter tuning
        self.clustering_configs = {
            'kmeans': [
                {'n_clusters': k, 'random_state': 42, 'n_init': 10}
                for k in range(2, 8)  # Limit range for production use
            ],
            'hierarchical': [
                {'n_clusters': k, 'linkage': linkage}
                for k in range(2, 8)
                for linkage in ['ward', 'complete', 'average']
            ]
        }
    
    def _load_embedding_model(self) -> None:
        """Load the sentence transformer model."""
        if not CLUSTERING_AVAILABLE:
            raise ImportError(
                "sentence-transformers not available. "
                "Install with: pip install sentence-transformers"
            )
        
        if self.embedding_model is None:
            self.logger.info(f"Loading embedding model: {self.embedding_model_name}")
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            self.logger.info(f"Embedding model loaded (dimension: {self.embedding_model.get_sentence_embedding_dimension()})")
    
    def _prepare_texts(self, article_summaries: List[Dict[str, Any]]) -> List[str]:
        """
        Prepare text content for embedding generation.
        
        Args:
            article_summaries: List of article summary dictionaries
            
        Returns:
            List of text strings for embedding
        """
        texts = []
        for article in article_summaries:
            # Use AI summary if available, otherwise fall back to title
            if article.get("summary"):
                text = f"{article.get('title', '')}\n{article['summary']}"
            else:
                text = article.get("title", "")
            
            # Add category and key points for better semantic representation
            if article.get("category"):
                text += f"\nCategory: {article['category']}"
            
            if article.get("key_points"):
                key_points = article["key_points"][:3]  # Limit to first 3 key points
                text += f"\nKey points: {'; '.join(key_points)}"
            
            texts.append(text.strip())
        
        return texts
    
    def _generate_embeddings(self, texts: List[str]) -> np.ndarray:
        """
        Generate embeddings for the given texts.
        
        Args:
            texts: List of text strings
            
        Returns:
            Numpy array of embeddings
        """
        self._load_embedding_model()
        
        self.logger.info(f"Generating embeddings for {len(texts)} texts")
        embeddings = self.embedding_model.encode(texts, show_progress_bar=False, batch_size=32)
        
        return embeddings
    
    def _evaluate_clustering(self, embeddings: np.ndarray, labels: np.ndarray, 
                           algorithm: str, parameters: Dict[str, Any]) -> ClusteringEvaluation:
        """
        Evaluate clustering quality using multiple metrics.
        
        Args:
            embeddings: The embeddings used for clustering
            labels: Cluster labels assigned by the algorithm
            algorithm: Name of the clustering algorithm
            parameters: Parameters used for the algorithm
            
        Returns:
            ClusteringEvaluation object with metrics
        """
        try:
            sil_score = silhouette_score(embeddings, labels)
        except:
            sil_score = -1.0
        
        try:
            ch_score = calinski_harabasz_score(embeddings, labels)
        except:
            ch_score = 0.0
        
        try:
            db_score = davies_bouldin_score(embeddings, labels)
        except:
            db_score = float('inf')
        
        return ClusteringEvaluation(
            silhouette_score=sil_score,
            calinski_harabasz_score=ch_score,
            davies_bouldin_score=db_score,
            num_clusters=len(set(labels)) - (1 if -1 in labels else 0),
            num_articles=len(labels),
            algorithm=algorithm,
            parameters=parameters
        )
    
    def _select_best_clustering(self, evaluations: Dict[str, ClusteringEvaluation]) -> Optional[str]:
        """
        Select the best clustering configuration based on evaluation metrics.
        
        Args:
            evaluations: Dictionary of clustering evaluations
            
        Returns:
            Name of the best clustering configuration
        """
        if not evaluations:
            return None
        
        # Score each configuration (higher is better)
        scored_configs = []
        
        for name, eval_result in evaluations.items():
            # Composite score combining multiple metrics
            sil_normalized = (eval_result.silhouette_score + 1) / 2
            ch_normalized = min(1.0, np.log(eval_result.calinski_harabasz_score + 1) / 10)
            db_capped = min(10.0, eval_result.davies_bouldin_score)
            db_normalized = 1 - (db_capped / 10)
            
            composite_score = (0.4 * sil_normalized + 
                             0.3 * ch_normalized + 
                             0.3 * db_normalized)
            
            scored_configs.append((name, composite_score, eval_result))
        
        # Sort by composite score (descending)
        scored_configs.sort(key=lambda x: x[1], reverse=True)
        
        best_config = scored_configs[0]
        self.logger.info(f"Best clustering configuration: {best_config[0]} (score: {best_config[1]:.3f})")
        
        return best_config[0]
    
    def _apply_clustering_algorithms(self, embeddings: np.ndarray) -> Dict[str, ClusteringEvaluation]:
        """
        Apply multiple clustering algorithms and evaluate their performance.
        
        Args:
            embeddings: Article embeddings
            
        Returns:
            Dictionary mapping algorithm names to their evaluation results
        """
        # Normalize embeddings for better clustering
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(embeddings)
        
        results = {}
        
        # Test K-means with different cluster counts
        for config in self.clustering_configs['kmeans']:
            try:
                kmeans = KMeans(**config)
                labels = kmeans.fit_predict(embeddings_scaled)
                
                # Skip if only one cluster or all points in separate clusters
                if len(set(labels)) <= 1 or len(set(labels)) >= len(embeddings) - 1:
                    continue
                
                evaluation = self._evaluate_clustering(embeddings_scaled, labels, "kmeans", config)
                results[f"kmeans_k{config['n_clusters']}"] = evaluation
                
            except Exception as e:
                self.logger.warning(f"K-means with {config} failed: {e}")
                continue
        
        # Test Hierarchical clustering
        for config in self.clustering_configs['hierarchical']:
            try:
                hierarchical = AgglomerativeClustering(**config)
                labels = hierarchical.fit_predict(embeddings_scaled)
                
                # Skip if only one cluster
                if len(set(labels)) <= 1:
                    continue
                
                evaluation = self._evaluate_clustering(embeddings_scaled, labels, "hierarchical", config)
                results[f"hierarchical_k{config['n_clusters']}_{config['linkage']}"] = evaluation
                
            except Exception as e:
                self.logger.warning(f"Hierarchical with {config} failed: {e}")
                continue
        
        self.logger.info(f"Completed clustering evaluation with {len(results)} valid configurations")
        return results

    def _perform_final_clustering(self, embeddings: np.ndarray, best_config_name: str) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        Perform the final clustering using the best configuration.

        Args:
            embeddings: Article embeddings
            best_config_name: Name of the best clustering configuration

        Returns:
            Tuple of (cluster_labels, centroids)
        """
        # Normalize embeddings
        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(embeddings)

        # Parse configuration name and apply clustering
        if best_config_name.startswith("kmeans"):
            k = int(best_config_name.split("_k")[1])
            clusterer = KMeans(n_clusters=k, random_state=42, n_init=10)
            labels = clusterer.fit_predict(embeddings_scaled)
            centroids = clusterer.cluster_centers_

        elif best_config_name.startswith("hierarchical"):
            parts = best_config_name.split("_")
            k = int(parts[1].replace("k", ""))
            linkage = parts[2]
            clusterer = AgglomerativeClustering(n_clusters=k, linkage=linkage)
            labels = clusterer.fit_predict(embeddings_scaled)
            centroids = None  # Hierarchical doesn't have centroids

        else:
            raise ValueError(f"Unknown clustering configuration: {best_config_name}")

        return labels, centroids

    def _create_cluster_objects(self, article_summaries: List[Dict[str, Any]],
                               labels: np.ndarray, ai_parser=None) -> List[ArticleCluster]:
        """
        Create ArticleCluster objects from clustering results.

        Args:
            article_summaries: Original article summaries
            labels: Cluster labels from clustering algorithm
            ai_parser: AI parser instance for cluster naming (optional)

        Returns:
            List of ArticleCluster objects
        """
        # Group articles by cluster
        cluster_groups = {}
        for i, label in enumerate(labels):
            if label not in cluster_groups:
                cluster_groups[label] = []
            cluster_groups[label].append(i)

        clusters = []
        for cluster_id, article_indices in cluster_groups.items():
            # Skip noise points in DBSCAN (label -1)
            if cluster_id == -1:
                continue

            # Get articles in this cluster
            cluster_articles = [article_summaries[i] for i in article_indices]

            # Generate cluster name and description
            if ai_parser and hasattr(ai_parser, '_name_cluster_with_ai'):
                try:
                    theme, description = ai_parser._name_cluster_with_ai(cluster_articles)
                except Exception as e:
                    self.logger.warning(f"AI cluster naming failed: {e}")
                    theme, description = self._generate_fallback_cluster_name(cluster_articles)
            else:
                theme, description = self._generate_fallback_cluster_name(cluster_articles)

            # Determine priority based on cluster size and categories
            priority = self._determine_cluster_priority(cluster_articles)

            cluster = ArticleCluster(
                cluster_id=f"cluster_{cluster_id}",
                theme=theme,
                description=description,
                article_indices=article_indices,
                priority=priority
            )
            clusters.append(cluster)

        return clusters

    def _generate_fallback_cluster_name(self, cluster_articles: List[Dict[str, Any]]) -> Tuple[str, str]:
        """
        Generate fallback cluster name and description based on article content.

        Args:
            cluster_articles: Articles in the cluster

        Returns:
            Tuple of (theme, description)
        """
        # Find most common category
        categories = [article.get("category", "Unknown") for article in cluster_articles]
        most_common_category = max(set(categories), key=categories.count)

        # Create theme and description
        if len(cluster_articles) == 1:
            theme = f"{most_common_category} Update"
            description = f"Single article about {most_common_category.lower()}"
        else:
            theme = f"{most_common_category} Developments"
            description = f"Articles related to {most_common_category.lower()}"

        return theme, description

    def _determine_cluster_priority(self, cluster_articles: List[Dict[str, Any]]) -> str:
        """
        Determine cluster priority based on article characteristics.

        Args:
            cluster_articles: Articles in the cluster

        Returns:
            Priority level (High, Medium, Low)
        """
        # Priority based on cluster size and categories
        high_priority_categories = {
            "Carbon pricing & markets", "Disclosure & reporting",
            "Energy transition", "Transport"
        }

        categories = [article.get("category", "") for article in cluster_articles]
        has_high_priority_category = any(cat in high_priority_categories for cat in categories)

        if len(cluster_articles) >= 3 and has_high_priority_category:
            return "High"
        elif len(cluster_articles) >= 2 or has_high_priority_category:
            return "Medium"
        else:
            return "Low"

    def cluster_articles(self, article_summaries: List[Dict[str, Any]],
                        ai_parser=None) -> ArticleClustering:
        """
        Main method to cluster articles using embedding-based approach.

        Args:
            article_summaries: List of article summary dictionaries
            ai_parser: AI parser instance for cluster naming (optional)

        Returns:
            ArticleClustering object compatible with existing interface
        """
        self.logger.info(f"Starting embedding-based clustering for {len(article_summaries)} articles")

        if not CLUSTERING_AVAILABLE:
            self.logger.warning("Clustering dependencies not available, falling back to category-based clustering")
            return self._fallback_category_clustering(article_summaries)

        if len(article_summaries) <= 1:
            # No clustering needed for single article
            return ArticleClustering(
                clusters=[ArticleCluster(
                    cluster_id="single_article",
                    theme="Daily Update",
                    description="Single article update",
                    article_indices=[0] if article_summaries else [],
                    priority="Medium"
                )]
            )

        try:
            # Step 1: Prepare texts and generate embeddings
            texts = self._prepare_texts(article_summaries)
            embeddings = self._generate_embeddings(texts)

            # Step 2: Apply clustering algorithms and find the best one
            evaluations = self._apply_clustering_algorithms(embeddings)

            if not evaluations:
                self.logger.warning("No valid clustering configurations found, using fallback")
                return self._fallback_category_clustering(article_summaries)

            best_config = self._select_best_clustering(evaluations)

            # Step 3: Perform final clustering
            labels, centroids = self._perform_final_clustering(embeddings, best_config)

            # Step 4: Create cluster objects
            clusters = self._create_cluster_objects(article_summaries, labels, ai_parser)

            # Ensure all articles are assigned to clusters
            assigned_indices = set()
            for cluster in clusters:
                assigned_indices.update(cluster.article_indices)

            # Create fallback cluster for unassigned articles
            unassigned = [i for i in range(len(article_summaries)) if i not in assigned_indices]
            if unassigned:
                clusters.append(ArticleCluster(
                    cluster_id="miscellaneous",
                    theme="Other Developments",
                    description="Additional regulatory and policy updates",
                    article_indices=unassigned,
                    priority="Low"
                ))

            self.logger.info(f"Successfully clustered articles into {len(clusters)} clusters")
            return ArticleClustering(clusters=clusters)

        except Exception as e:
            self.logger.warning(f"Embedding clustering failed: {e}, using fallback")
            return self._fallback_category_clustering(article_summaries)

    def _fallback_category_clustering(self, article_summaries: List[Dict[str, Any]]) -> ArticleClustering:
        """
        Fallback clustering method that groups articles by category.

        Args:
            article_summaries: List of article summary dictionaries

        Returns:
            ArticleClustering object with category-based clusters
        """
        category_clusters = {}
        for i, article in enumerate(article_summaries):
            category = article.get("category", "Unknown")
            if category not in category_clusters:
                category_clusters[category] = []
            category_clusters[category].append(i)

        clusters = []
        for category, indices in category_clusters.items():
            clusters.append(ArticleCluster(
                cluster_id=category.lower().replace(" ", "_").replace("&", "and"),
                theme=category,
                description=f"Articles related to {category.lower()}",
                article_indices=indices,
                priority="Medium"
            ))

        return ArticleClustering(clusters=clusters)
