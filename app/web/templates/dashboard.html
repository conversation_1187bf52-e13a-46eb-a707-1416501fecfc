{% extends "base.html" %}

{% block title %}Dashboard - Carbon Regulation News{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p class="mt-2 text-gray-600">Overview of the Carbon Regulation News monitoring system</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Articles -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">📰</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Articles</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ total_articles }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processed Articles -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">✅</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Processed Articles</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ processed_articles }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unprocessed Articles -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">⏳</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Unprocessed Articles</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ unprocessed_articles }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Manual Task Trigger -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Manual Task Trigger</h3>
                <div class="space-y-4">
                    <p class="text-sm text-gray-600">
                        Manually trigger a full pipeline task to collect news, process with AI, and generate a daily summary.
                    </p>
                    <div class="flex items-center space-x-3">
                        <button
                            id="trigger-pipeline-btn"
                            onclick="triggerFullPipeline()"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span id="trigger-btn-text">🚀 Run Full Pipeline</span>
                            <span id="trigger-btn-spinner" class="hidden ml-2">
                                <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                        </button>
                        <div id="trigger-status" class="text-sm"></div>
                    </div>
                    <div id="trigger-result" class="hidden">
                        <div class="bg-green-50 border border-green-200 rounded-md p-3">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-green-800" id="trigger-success-message"></p>
                                    <p class="text-sm text-green-700 mt-1">
                                        <a href="/dashboard/tasks" class="underline">View task progress →</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="trigger-error" class="hidden">
                        <div class="bg-red-50 border border-red-200 rounded-md p-3">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-red-800">Failed to trigger task</p>
                                    <p class="text-sm text-red-700 mt-1" id="trigger-error-message"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Latest Summary -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Latest Daily Summary</h3>
                {% if latest_summary %}
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm text-gray-500">Generated:</span>
                            <span class="text-sm text-gray-900">{{ latest_summary.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">{{ latest_summary.title }}</h4>
                        </div>
                        {% if latest_summary.result_metadata and latest_summary.result_metadata.get('markdown_summary') %}
                            <div class="bg-gray-50 p-4 rounded-md">
                                <div id="latest-summary-markdown" data-markdown class="text-sm text-gray-700">{{ latest_summary.result_metadata.get('markdown_summary') }}</div>
                            </div>
                        {% else %}
                            <div class="bg-gray-50 p-4 rounded-md">
                                <p class="text-sm text-gray-700">{{ latest_summary.summary[:300] }}{% if latest_summary.summary|length > 300 %}...{% endif %}</p>
                            </div>
                        {% endif %}
                        <div class="pt-2">
                            <a href="/dashboard/summaries/{{ latest_summary.id }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                View Full Summary →
                            </a>
                        </div>
                    </div>
                {% else %}
                    <p class="text-gray-500">No daily summaries generated yet.</p>
                {% endif %}
            </div>
        </div>

        <!-- Recent Tasks -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Tasks</h3>
                {% if recent_tasks %}
                    <div class="space-y-3">
                        {% for task in recent_tasks %}
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm font-medium text-gray-900">{{ task.task_name }}</span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 'bg-green-100 text-green-800' if task.status == 'success' else 'bg-red-100 text-red-800' if task.status == 'failed' else 'bg-blue-100 text-blue-800' if task.status == 'running' else 'bg-gray-100 text-gray-800' }}">
                                            {{ task.status }}
                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        {{ task.started_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% if task.duration_seconds %}
                                            • {{ "%.1f"|format(task.duration_seconds) }}s
                                        {% endif %}
                                    </div>
                                </div>
                                <a href="/dashboard/tasks/{{ task.id }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                    View →
                                </a>
                            </div>
                        {% endfor %}
                    </div>
                    <div class="mt-4 pt-4 border-t">
                        <a href="/dashboard/tasks" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View All Tasks →
                        </a>
                    </div>
                {% else %}
                    <p class="text-gray-500">No tasks executed yet.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="/dashboard/articles" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    📰 View Articles
                </a>
                <a href="/dashboard/summaries" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    📊 View Summaries
                </a>
                <a href="/dashboard/tasks" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    ⚙️ View Tasks
                </a>
                <a href="/docs" target="_blank" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    📖 API Docs
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Render markdown content for the latest summary
    document.addEventListener('DOMContentLoaded', function() {
        renderMarkdown('latest-summary-markdown');
    });

    // Manual task trigger functionality
    async function triggerFullPipeline() {
        const button = document.getElementById('trigger-pipeline-btn');
        const buttonText = document.getElementById('trigger-btn-text');
        const spinner = document.getElementById('trigger-btn-spinner');
        const resultDiv = document.getElementById('trigger-result');
        const errorDiv = document.getElementById('trigger-error');
        const successMessage = document.getElementById('trigger-success-message');
        const errorMessage = document.getElementById('trigger-error-message');

        // Reset UI state
        button.disabled = true;
        buttonText.textContent = 'Triggering...';
        spinner.classList.remove('hidden');
        resultDiv.classList.add('hidden');
        errorDiv.classList.add('hidden');

        try {
            const response = await fetch('/tasks/trigger', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_type: 'full_pipeline'
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // Success
                successMessage.textContent = data.message;
                resultDiv.classList.remove('hidden');

                // Auto-refresh the page after 2 seconds to show updated task list
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                // API returned error
                errorMessage.textContent = data.message || 'Unknown error occurred';
                errorDiv.classList.remove('hidden');
            }
        } catch (error) {
            // Network or other error
            errorMessage.textContent = 'Network error: ' + error.message;
            errorDiv.classList.remove('hidden');
        } finally {
            // Reset button state
            button.disabled = false;
            buttonText.textContent = '🚀 Run Full Pipeline';
            spinner.classList.add('hidden');
        }
    }
</script>
{% endblock %}
