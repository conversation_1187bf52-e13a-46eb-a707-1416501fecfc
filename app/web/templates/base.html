<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Carbon Regulation News{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        /* Custom styles for markdown content */
        .markdown-content {
            line-height: 1.6;
        }
        .markdown-content h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            margin-top: 1.5rem;
            color: #1f2937;
        }
        .markdown-content h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            margin-top: 1.25rem;
            color: #374151;
        }
        .markdown-content h3 {
            font-size: 1.125rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            margin-top: 1rem;
            color: #4b5563;
        }
        .markdown-content h4 {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            margin-top: 0.75rem;
            color: #6b7280;
        }
        .markdown-content p {
            margin-bottom: 0.75rem;
            color: #374151;
        }
        .markdown-content ul {
            list-style-type: disc;
            list-style-position: inside;
            margin-bottom: 0.75rem;
            margin-left: 1rem;
        }
        .markdown-content ol {
            list-style-type: decimal;
            list-style-position: inside;
            margin-bottom: 0.75rem;
            margin-left: 1rem;
        }
        .markdown-content li {
            margin-bottom: 0.25rem;
        }
        .markdown-content blockquote {
            border-left: 4px solid #d1d5db;
            padding-left: 1rem;
            font-style: italic;
            margin-bottom: 0.75rem;
            color: #6b7280;
        }
        .markdown-content code {
            background-color: #f3f4f6;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-family: 'Courier New', monospace;
        }
        .markdown-content pre {
            background-color: #f3f4f6;
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin-bottom: 0.75rem;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        .markdown-content a {
            color: #2563eb;
            text-decoration: underline;
        }
        .markdown-content a:hover {
            color: #1d4ed8;
        }
        .markdown-content strong {
            font-weight: 600;
        }
        .markdown-content em {
            font-style: italic;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex-shrink-0">
                    <a href="/dashboard/" class="text-xl font-bold text-gray-900">
                        🌱 Carbon Regulation News
                    </a>
                </div>
                <div class="hidden sm:flex sm:space-x-8">
                    <a href="/dashboard/" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Dashboard
                    </a>
                    <a href="/dashboard/articles" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Articles
                    </a>
                    <a href="/dashboard/summaries" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Summaries
                    </a>
                    <a href="/dashboard/tasks" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Tasks
                    </a>
                </div>
                <div class="flex items-center">
                    <a href="/docs" target="_blank" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                        API Docs
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="text-center text-sm text-gray-500">
                Carbon Regulation News System - Monitoring environmental policy changes
            </div>
        </div>
    </footer>

    <script>
        // Function to render markdown content
        function renderMarkdown(elementId) {
            // Check if marked library is loaded
            if (typeof marked === 'undefined') {
                console.error('Marked library not loaded');
                return;
            }

            const element = document.getElementById(elementId);
            if (element) {
                let markdownText = element.textContent || element.innerText;

                // Clean up escaped characters
                const cleanedText = markdownText
                    .replace(/\\n/g, '\n')
                    .replace(/\\t/g, '\t')
                    .replace(/\\"/g, '"')
                    .replace(/\\'/g, "'")
                    .replace(/\\r/g, '\r');

                // Parse markdown and set HTML
                const htmlContent = marked.parse(cleanedText);
                element.innerHTML = htmlContent;
                element.classList.add('markdown-content');

                // Debug log
                console.log('Rendered markdown for:', elementId);
            } else {
                console.log('Element not found:', elementId);
            }
        }

        // Function to render JSON content
        function renderJSON(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                try {
                    const jsonText = element.textContent || element.innerText;
                    const jsonData = JSON.parse(jsonText);
                    const formattedJSON = JSON.stringify(jsonData, null, 2);

                    element.innerHTML = `<pre class="bg-gray-100 p-4 rounded-lg overflow-x-auto"><code class="language-json">${escapeHtml(formattedJSON)}</code></pre>`;
                    element.classList.add('json-content');

                    console.log('Rendered JSON for:', elementId);
                } catch (e) {
                    console.error('Failed to parse JSON for:', elementId, e);
                }
            } else {
                console.log('Element not found:', elementId);
            }
        }

        // Helper function to escape HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Function to format dates
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }

        // Function to format duration
        function formatDuration(seconds) {
            if (seconds < 60) {
                return seconds.toFixed(1) + 's';
            } else if (seconds < 3600) {
                return (seconds / 60).toFixed(1) + 'm';
            } else {
                return (seconds / 3600).toFixed(1) + 'h';
            }
        }

        // Function to get status badge class
        function getStatusBadgeClass(status) {
            switch(status) {
                case 'success':
                    return 'bg-green-100 text-green-800';
                case 'failed':
                    return 'bg-red-100 text-red-800';
                case 'running':
                    return 'bg-blue-100 text-blue-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        // Auto-refresh functionality for dashboard
        function setupAutoRefresh() {
            if (window.location.pathname === '/dashboard/' || window.location.pathname === '/dashboard') {
                setInterval(() => {
                    window.location.reload();
                }, 30000); // Refresh every 30 seconds
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Render any markdown content
            const markdownElements = document.querySelectorAll('[data-markdown]');
            markdownElements.forEach(element => {
                renderMarkdown(element.id);
            });

            // Setup auto-refresh for dashboard
            setupAutoRefresh();

            // Add active class to current nav item
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('nav a');
            navLinks.forEach(link => {
                const linkHref = link.getAttribute('href');
                let isActive = false;

                if (linkHref === currentPath) {
                    // Exact match
                    isActive = true;
                } else if (linkHref === '/dashboard/' && (currentPath === '/dashboard' || currentPath === '/dashboard/')) {
                    // Dashboard exact match (handle both /dashboard and /dashboard/)
                    isActive = true;
                } else if (linkHref !== '/dashboard/' && linkHref !== '/docs' && currentPath.startsWith(linkHref)) {
                    // For non-dashboard links, use startsWith (but exclude dashboard and docs)
                    isActive = true;
                }

                if (isActive) {
                    link.classList.remove('border-transparent', 'text-gray-500');
                    link.classList.add('border-blue-500', 'text-blue-600');
                }
            });
        });
    </script>
</body>
</html>
