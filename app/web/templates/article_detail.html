{% extends "base.html" %}

{% block title %}{{ article.title }} - Carbon Regulation News{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="/dashboard/" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <a href="/dashboard/articles" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">Articles</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Article #{{ article.id }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Article Header -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h1 class="text-2xl font-bold text-gray-900 mb-4">{{ article.title }}</h1>
                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-4">
                        <span class="flex items-center">
                            <strong class="text-gray-700">Source:</strong>
                            <span class="ml-1">{{ article.source_name }}</span>
                        </span>
                        <span class="flex items-center">
                            <strong class="text-gray-700">Collected:</strong>
                            <span class="ml-1">{{ article.collected_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </span>
                        {% if article.published_date %}
                            <span class="flex items-center">
                                <strong class="text-gray-700">Published:</strong>
                                <span class="ml-1">{{ article.published_date.strftime('%Y-%m-%d') }}</span>
                            </span>
                        {% endif %}
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ 'bg-green-100 text-green-800' if article.is_processed else 'bg-yellow-100 text-yellow-800' }}">
                            {{ 'Processed' if article.is_processed else 'Pending Processing' }}
                        </span>
                        {% if article.ai_classification and article.ai_classification.get('category') %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ article.ai_classification.get('category') }}
                            </span>
                        {% endif %}
                        {% if article.ai_classification and article.ai_classification.get('type') %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                {{ article.ai_classification.get('type') }}
                            </span>
                        {% endif %}
                    </div>
                </div>
                <div class="ml-4">
                    <a href="{{ article.url }}" target="_blank" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        View Original
                        <svg class="ml-2 -mr-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- AI Summary -->
            {% if article.ai_summary %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">AI Summary</h3>
                        <div class="prose prose-sm max-w-none">
                            <p class="text-gray-700">{{ article.ai_summary }}</p>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Key Points -->
            {% if article.ai_key_points %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Key Points</h3>
                        <ul class="space-y-2">
                            {% for point in article.ai_key_points %}
                                <li class="flex items-start">
                                    <span class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></span>
                                    <span class="text-gray-700">{{ point }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            {% endif %}

            <!-- Article Content -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Full Content</h3>
                    <div class="prose prose-sm max-w-none">
                        <div class="text-gray-700 whitespace-pre-wrap">{{ article.content }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- AI Classification -->
            {% if article.ai_classification %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">AI Classification</h3>
                        <dl class="space-y-3">
                            {% if article.ai_classification.get('category') %}
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Category</dt>
                                    <dd class="text-sm text-gray-900">{{ article.ai_classification.get('category') }}</dd>
                                </div>
                            {% endif %}
                            {% if article.ai_classification.get('type') %}
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Type</dt>
                                    <dd class="text-sm text-gray-900">{{ article.ai_classification.get('type') }}</dd>
                                </div>
                            {% endif %}
                            {% if article.ai_classification.get('jurisdictions') %}
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Jurisdictions</dt>
                                    <dd class="text-sm text-gray-900">
                                        {% for jurisdiction in article.ai_classification.get('jurisdictions') %}
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-1 mb-1">
                                                {{ jurisdiction }}
                                            </span>
                                        {% endfor %}
                                    </dd>
                                </div>
                            {% endif %}
                            {% if article.ai_classification.get('sectors') %}
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Sectors</dt>
                                    <dd class="text-sm text-gray-900">
                                        {% for sector in article.ai_classification.get('sectors') %}
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-1 mb-1">
                                                {{ sector }}
                                            </span>
                                        {% endfor %}
                                    </dd>
                                </div>
                            {% endif %}
                        </dl>
                    </div>
                </div>
            {% endif %}

            <!-- AI Details -->
            {% if article.ai_details %}
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Additional Details</h3>
                        <div class="text-sm text-gray-700">
                            <pre class="whitespace-pre-wrap bg-gray-50 p-3 rounded">{{ article.ai_details | tojson(indent=2) }}</pre>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Processing Info -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Processing Info</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="text-sm text-gray-900">
                                {{ 'Processed' if article.is_processed else 'Pending' }}
                            </dd>
                        </div>
                        {% if article.processed_at %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Processed At</dt>
                                <dd class="text-sm text-gray-900">{{ article.processed_at.strftime('%Y-%m-%d %H:%M:%S') }}</dd>
                            </div>
                        {% endif %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Article ID</dt>
                            <dd class="text-sm text-gray-900">{{ article.id }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
