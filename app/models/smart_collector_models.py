"""
Models for the smart news collector with AI-driven per-source instructions.
"""

from typing import List, Optional, Dict, Any, Union, Literal
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class CollectedArticle(BaseModel):
    """Model for a collected news article before database storage"""
    title: str
    url: str
    content: str
    source_name: str
    published_date: Optional[datetime] = None


class ToolType(str, Enum):
    """Available tool types for the smart collector agent."""
    SEARCH_WEB = "search_web"
    EXTRACT_CONTENT = "extract_content"
    EXTRACT_URLS = "extract_urls"
    PARSE_RSS = "parse_rss"
    FILTER_ARTICLES = "filter_articles"
    EXTRACT_METADATA = "extract_metadata"


class ToolDefinition(BaseModel):
    """Definition of a tool available to the smart collector agent."""

    name: str = Field(..., description="Tool name")
    type: ToolType = Field(..., description="Tool type")
    description: str = Field(..., description="Description of what the tool does")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Tool-specific parameters")


class SmartSourceType(str, Enum):
    """Types of smart sources."""
    WEB_SEARCH = "web_search"
    SPECIFIC_URL = "specific_url"
    RSS_FEED = "rss_feed"
    CUSTOM = "custom"


class SmartSource(BaseModel):
    """Configuration for a smart news source with AI instructions."""

    name: str = Field(..., description="Human-readable name for the source")
    type: SmartSourceType = Field(..., description="Type of source")
    url: Optional[str] = Field(None, description="URL for the source (if applicable)")
    query: Optional[str] = Field(None, description="Search query (for web_search type)")

    # AI Instructions
    instructions: str = Field(..., description="Detailed instructions for the AI agent on how to collect from this source")

    # Tool configuration
    available_tools: List[ToolType] = Field(
        default_factory=lambda: [ToolType.SEARCH_WEB, ToolType.EXTRACT_CONTENT, ToolType.EXTRACT_URLS],
        description="List of tools available for this source"
    )

    # Source-specific settings
    max_articles: Optional[int] = Field(None, description="Maximum articles to collect from this source")
    time_range: Optional[str] = Field(None, description="Time range for collection (e.g., 'day', 'week')")

    # Additional parameters for the source
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Additional source-specific parameters")

    # Backward compatibility flag
    enabled: bool = Field(default=True, description="Whether this source is enabled")


class ToolExecution(BaseModel):
    """Result of executing a tool."""

    tool_name: str = Field(..., description="Name of the executed tool")
    tool_type: ToolType = Field(..., description="Type of tool executed")
    success: bool = Field(..., description="Whether the tool execution was successful")
    result: Any = Field(None, description="Result data from the tool")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    execution_time: float = Field(..., description="Time taken to execute the tool in seconds")


class SmartCollectionPlan(BaseModel):
    """AI-generated plan for collecting from a smart source."""

    source_name: str = Field(..., description="Name of the source")
    reasoning: str = Field(..., description="AI's reasoning for the chosen approach")
    tools_to_use: List[ToolType] = Field(..., description="Ordered list of tools to execute")
    tool_parameters: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Parameters for each tool"
    )
    expected_outcome: str = Field(..., description="What the AI expects to achieve")


class SmartCollectionResult(BaseModel):
    """Result of smart collection from a source."""

    source_name: str = Field(..., description="Name of the source")
    plan: SmartCollectionPlan = Field(..., description="The execution plan that was used")
    tool_executions: List[ToolExecution] = Field(default_factory=list, description="List of tool executions")
    articles_found: int = Field(default=0, description="Number of articles found")
    success: bool = Field(..., description="Whether the collection was successful")
    error: Optional[str] = Field(None, description="Error message if collection failed")
    total_time: float = Field(..., description="Total time taken for collection")


# Backward compatibility models
class LegacySourceConfig(BaseModel):
    """Legacy source configuration for backward compatibility."""

    search_queries: List[str] = Field(default_factory=list, description="Legacy search queries")
    specific_sources: List[str] = Field(default_factory=list, description="Legacy specific source URLs")
    rss_feeds: List[str] = Field(default_factory=list, description="Legacy RSS feed URLs")


class SmartNewsCollectorConfig(BaseModel):
    """Enhanced news collector configuration with smart sources."""

    # Legacy configuration (maintained for backward compatibility)
    max_articles_per_source: int = Field(default=10, description="Maximum articles per source")
    default_time_range: str = Field(default="day", description="Default time range for news search")

    # Legacy sources (backward compatibility)
    search_queries: List[str] = Field(default_factory=list, description="Legacy search queries")
    specific_sources: List[str] = Field(default_factory=list, description="Legacy specific source URLs")
    rss_feeds: List[str] = Field(default_factory=list, description="Legacy RSS feed URLs")

    # New smart sources
    smart_sources: List[SmartSource] = Field(default_factory=list, description="AI-driven smart sources")

    # Smart collector settings
    enable_smart_collection: bool = Field(default=True, description="Enable smart collection features")
    smart_agent_model: str = Field(default="openai/gpt-4.1-mini", description="Model for smart collection agent")
    smart_agent_temperature: float = Field(default=0.3, description="Temperature for smart collection agent")

    # Tool availability
    available_tools: List[ToolDefinition] = Field(default_factory=list, description="Available tools for smart collection")

    # Existing search settings (maintained for compatibility)
    search_settings: Optional[Dict[str, Any]] = Field(None, description="Search settings")


# Tool result models for structured outputs
class WebSearchResult(BaseModel):
    """Result from web search tool."""

    query: str = Field(..., description="Search query used")
    results: List[Dict[str, Any]] = Field(default_factory=list, description="Search results")
    total_results: int = Field(default=0, description="Total number of results found")


class ContentExtractionResult(BaseModel):
    """Result from content extraction tool."""

    url: str = Field(..., description="URL that was extracted")
    title: Optional[str] = Field(None, description="Extracted title")
    content: str = Field(..., description="Extracted content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class URLExtractionResult(BaseModel):
    """Result from URL extraction tool."""

    source_url: str = Field(..., description="Source URL that was analyzed")
    extracted_urls: List[str] = Field(default_factory=list, description="Extracted article URLs")
    total_urls: int = Field(default=0, description="Total number of URLs extracted")


class RSSParseResult(BaseModel):
    """Result from RSS parsing tool."""

    feed_url: str = Field(..., description="RSS feed URL")
    feed_title: Optional[str] = Field(None, description="Feed title")
    entries: List[Dict[str, Any]] = Field(default_factory=list, description="RSS entries")
    total_entries: int = Field(default=0, description="Total number of entries")


class ArticleFilterResult(BaseModel):
    """Result from article filtering tool."""

    original_count: int = Field(..., description="Number of articles before filtering")
    filtered_count: int = Field(..., description="Number of articles after filtering")
    filter_criteria: str = Field(..., description="Criteria used for filtering")
    filtered_articles: List[Dict[str, Any]] = Field(default_factory=list, description="Filtered articles")