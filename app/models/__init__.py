"""
Models package for the carbon regulation news application.
"""

from .ai_models import (
    # Enums
    Category,
    ItemType,
    Instrument,
    LegislationStage,
    JurisdictionLevel,
    ProposedStage,
    RegulatoryAction,
    Outcome,
    IncentiveType,
    Framework,

    # Basic models
    AIClassification,
    AIContent,
    Money,
    ImportantDate,

    # URL extraction
    ArticleURLs,

    # Clustering
    ArticleCluster,
    ArticleClustering,

    # Summary models
    DailySummary,
    EnhancedSummary,

    # Detail models
    RegulatoryUpdateDetails,
    ProposedRuleDetails,
    LegislationDetails,
    CourtEnforcementDetails,
    GuidanceStandardDetails,
    MarketAuctionDetails,
    CorporateDisclosureDetails,
    FundingIncentiveDetails,
    EventDetails,
    ResearchReportDetails,
    Details,
)

from .smart_collector_models import (
    # Enums
    ToolType,
    SmartSourceType,

    # Basic models
    CollectedArticle,

    # Smart collector models
    ToolDefinition,
    SmartSource,
    ToolExecution,
    SmartCollectionPlan,
    SmartCollectionResult,
    SmartNewsCollectorConfig,
    LegacySourceConfig,

    # Tool result models
    WebSearchResult,
    ContentExtractionResult,
    URLExtractionResult,
    RSSParseResult,
    ArticleFilterResult,
)

__all__ = [
    # Enums
    "Category",
    "ItemType",
    "Instrument",
    "LegislationStage",
    "JurisdictionLevel",
    "ProposedStage",
    "RegulatoryAction",
    "Outcome",
    "IncentiveType",
    "Framework",
    "ToolType",
    "SmartSourceType",

    # Basic models
    "AIClassification",
    "AIContent",
    "Money",
    "ImportantDate",

    # URL extraction
    "ArticleURLs",

    # Clustering
    "ArticleCluster",
    "ArticleClustering",

    # Summary models
    "DailySummary",
    "EnhancedSummary",

    # Detail models
    "RegulatoryUpdateDetails",
    "ProposedRuleDetails",
    "LegislationDetails",
    "CourtEnforcementDetails",
    "GuidanceStandardDetails",
    "MarketAuctionDetails",
    "CorporateDisclosureDetails",
    "FundingIncentiveDetails",
    "EventDetails",
    "ResearchReportDetails",
    "Details",

    # Smart collector models
    "CollectedArticle",
    "ToolDefinition",
    "SmartSource",
    "ToolExecution",
    "SmartCollectionPlan",
    "SmartCollectionResult",
    "SmartNewsCollectorConfig",
    "LegacySourceConfig",
    "WebSearchResult",
    "ContentExtractionResult",
    "URLExtractionResult",
    "RSSParseResult",
    "ArticleFilterResult",
]
