"""
Pydantic models for AI-powered structured output.

These models define the expected structure for AI responses to ensure
reliable and type-safe output from pydantic-ai agents.
"""

from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import List, Optional, Union
from uuid import UUID, uuid4

from pydantic import AnyUrl, BaseModel, Field, ConfigDict


# ------ Enums for classification ------

class Category(str, Enum):
    """High-level policy focus of the item."""
    
    CARBON_PRICING_MARKETS = "Carbon pricing & markets"
    DISCLOSURE_REPORTING = "Disclosure & reporting"
    SECTOR_STANDARDS = "Sector standards"
    ENERGY_TRANSITION = "Energy transition"
    TRANSPORT = "Transport"
    BUILDINGS = "Buildings"
    AGRI_LAND_USE = "Agriculture & land-use"
    OFFSETS_REMOVALS = "Offsets & removals"
    FINANCE_ESG = "Finance & ESG"
    LITIGATION_ENFORCEMENT = "Litigation & enforcement"
    INTERNATIONAL_TRADE = "International & trade"
    OTHER = "Other"
    UNKNOWN = "Unknown"


class ItemType(str, Enum):
    """Content form (exactly one per article)."""
    
    REGULATORY_UPDATE = "Regulatory Update (Final)"
    PROPOSED_RULE = "Proposed Rule"
    LEGISLATION = "Legislation"
    COURT_ENFORCEMENT = "Court/Enforcement"
    GUIDANCE_STANDARD = "Guidance/Standard"
    MARKET_AUCTION = "Market/Auction"
    CORPORATE_DISCLOSURE = "Corporate Disclosure"
    FUNDING_INCENTIVE = "Funding/Incentive"
    EVENT = "Event"
    RESEARCH_REPORT = "Research/Report"
    OTHER = "Other"
    UNKNOWN = "Unknown"


class Instrument(str, Enum):
    """Policy instrument summarized in the update."""
    
    ETS = "ETS"
    CARBON_TAX = "Carbon Tax"
    STANDARD = "Standard"
    CBAM = "CBAM"
    OTHER = "Other"
    UNSPECIFIED = "Unspecified"


class LegislationStage(str, Enum):
    """Lifecycle stage of a bill or act."""
    
    INTRODUCED = "introduced"
    PASSED = "passed"
    SIGNED = "signed"
    VETOED = "vetoed"
    UNSPECIFIED = "unspecified"


class JurisdictionLevel(str, Enum):
    """Administrative level of legislation or policy."""
    
    NATIONAL = "national"
    STATE = "state"
    LOCAL = "local"
    UNSPECIFIED = "unspecified"


class ProposedStage(str, Enum):
    """Stage of a proposal during rulemaking or consultation."""
    
    PROPOSED = "proposed"
    DRAFT = "draft"
    CONSULTATION = "consultation"
    UNSPECIFIED = "unspecified"


class RegulatoryAction(str, Enum):
    """Action taken on a rule or regulation."""
    
    ADOPTED = "adopted"
    ENTERED_INTO_FORCE = "entered_into_force"
    AMENDED = "amended"
    REPEALED = "repealed"
    UNSPECIFIED = "unspecified"


class Outcome(str, Enum):
    """Result of a court decision or enforcement action."""
    
    UPHELD = "upheld"
    STRUCK = "struck"
    REMANDED = "remanded"
    PENALIZED = "penalized"
    INVESTIGATION_OPENED = "investigation_opened"
    UNSPECIFIED = "unspecified"


class IncentiveType(str, Enum):
    """Type of public support or financial incentive."""
    
    GRANT = "grant"
    TAX_CREDIT = "tax_credit"
    REBATE = "rebate"
    LOAN = "loan"
    TENDER = "tender"
    UNSPECIFIED = "unspecified"


class Framework(str, Enum):
    """Disclosure/reporting or target framework referenced by companies."""
    
    CSRD = "CSRD"
    SEC = "SEC"
    ISSB = "ISSB"
    TCFD = "TCFD"
    SBTI = "SBTi"
    OTHER = "Other"
    UNSPECIFIED = "Unspecified"


# ------ Basic building blocks ------

class AIClassification(BaseModel):
    """AI-determined labels describing what the item is about."""
    
    model_config = ConfigDict(
        title="AIClassification",
        json_schema_extra={
            "description": "AI-determined labels for category, type, jurisdictions, and sectors."
        }
    )
    
    category: Category = Field(..., description="Primary high-level policy category")
    type: ItemType = Field(..., description="Content form/type (exactly one)")
    jurisdictions: List[str] = Field(
        default_factory=list,
        description="Jurisdictions mentioned (ISO-3166 codes or region strings; primary first)"
    )
    sectors: Optional[List[str]] = Field(
        default=None,
        description="Optional sector tags (e.g., power, transport, industry). Free-form and lightweight."
    )


class AIContent(BaseModel):
    """Normalized content extracted by AI from the article."""
    
    model_config = ConfigDict(
        title="AIContent",
        json_schema_extra={
            "description": "AI-normalized content: title, short summary, key points, and original text."
        }
    )
    
    title: str = Field(..., description="Article title (normalized)")
    summary: str = Field(..., description="2–4 sentence summary of the item")
    key_points: List[str] = Field(
        default_factory=list,
        description="3–7 bullet points capturing the essentials"
    )
    original_text: str = Field(..., description="Original extracted text of the article (cleaned)")


class Money(BaseModel):
    """Simple monetary value used where price is reported."""
    
    model_config = ConfigDict(
        title="Money",
        json_schema_extra={
            "description": "Simple monetary value with numeric amount and ISO currency code."
        }
    )
    
    amount: float = Field(..., description="Numeric amount (e.g., 90.25)")
    currency: str = Field(..., description="Currency code (e.g., USD, EUR)")


class ImportantDate(BaseModel):
    """An important date with description for tracking timelines and deadlines."""
    
    model_config = ConfigDict(
        title="ImportantDate",
        json_schema_extra={
            "description": "Important date with contextual description for tracking timelines."
        }
    )
    
    date: datetime = Field(..., description="The important date in ISO8601 format")
    description: str = Field(
        ...,
        description="Description of what happens on this date (e.g., 'Preliminary countervailing duty determination')"
    )
    is_estimate: bool = Field(default=False, description="Whether this date is estimated/approximate")


# ------ URL extraction models ------

class ArticleURLs(BaseModel):
    """Model for extracted article URLs from web pages."""
    
    model_config = ConfigDict(
        title="ArticleURLs",
        json_schema_extra={
            "description": "List of article URLs extracted from a web page."
        }
    )
    
    urls: List[str] = Field(
        default_factory=list,
        description="List of article URLs found on the page"
    )


# ------ Clustering models ------

class ArticleCluster(BaseModel):
    """A cluster of related articles grouped by theme."""
    
    model_config = ConfigDict(
        title="ArticleCluster",
        json_schema_extra={
            "description": "A cluster of related articles grouped by theme or topic."
        }
    )
    
    cluster_id: str = Field(..., description="Unique identifier for the cluster")
    theme: str = Field(..., description="Main theme or topic of the cluster")
    description: str = Field(..., description="Brief description of what the cluster contains")
    article_indices: List[int] = Field(
        default_factory=list,
        description="Indices of articles that belong to this cluster"
    )
    priority: str = Field(
        default="Medium",
        description="Priority level of this cluster (High, Medium, Low)"
    )


class ArticleClustering(BaseModel):
    """Result of AI-powered article clustering."""
    
    model_config = ConfigDict(
        title="ArticleClustering",
        json_schema_extra={
            "description": "Result of clustering articles by themes and topics."
        }
    )
    
    clusters: List[ArticleCluster] = Field(
        default_factory=list,
        description="List of article clusters"
    )


# ------ Summary models ------

class DailySummary(BaseModel):
    """AI-generated daily summary in structured format."""
    
    model_config = ConfigDict(
        title="DailySummary",
        json_schema_extra={
            "description": "AI-generated daily summary with key developments and insights."
        }
    )
    
    executive_summary: str = Field(..., description="Executive summary of the day's developments")
    key_developments: List[str] = Field(
        default_factory=list,
        description="List of key developments and highlights"
    )
    regulatory_changes: List[str] = Field(
        default_factory=list,
        description="Important regulatory changes and updates"
    )
    market_implications: List[str] = Field(
        default_factory=list,
        description="Market implications and trends"
    )
    important_dates: List[str] = Field(
        default_factory=list,
        description="Important upcoming dates and deadlines"
    )


class EnhancedSummary(BaseModel):
    """Enhanced markdown summary with structured metadata."""

    model_config = ConfigDict(
        title="EnhancedSummary",
        json_schema_extra={
            "description": "Enhanced daily summary in markdown format with metadata."
        }
    )

    markdown_content: str = Field(..., description="Full markdown-formatted summary")
    executive_summary: str = Field(..., description="Executive summary extracted from markdown")
    key_themes: List[str] = Field(
        default_factory=list,
        description="Key themes identified in the summary"
    )
    priority_items: List[str] = Field(
        default_factory=list,
        description="High-priority items requiring attention"
    )


# ------ Type-specific detail models ------

class RegulatoryUpdateDetails(BaseModel):
    """Officially adopted or in-force regulatory change."""

    model_config = ConfigDict(
        title="RegulatoryUpdateDetails",
        json_schema_extra={
            "description": "Details for final regulatory updates (adopted, entered into force, amended, repealed)."
        }
    )

    regulator: Optional[str] = Field(default=None, description="Issuing regulator or ministry (e.g., EPA, EC)")
    instrument: Optional[Instrument] = Field(default=None, description="Policy instrument if clear (e.g., ETS, Carbon Tax)")
    action: RegulatoryAction = Field(..., description="Action taken on the rule (adopted, entered into force, etc.)")
    effective_date: Optional[datetime] = Field(default=None, description="Date the change takes effect, if specified")
    important_dates: List[ImportantDate] = Field(
        default_factory=list,
        description="Timeline of important dates including implementation milestones"
    )
    scope_note: Optional[str] = Field(default=None, description="Short free-text description of scope/coverage")


class ProposedRuleDetails(BaseModel):
    """Draft proposal or consultation prior to a final rule."""

    model_config = ConfigDict(
        title="ProposedRuleDetails",
        json_schema_extra={
            "description": "Details for proposed rules or consultations, including stage and key dates."
        }
    )

    regulator: Optional[str] = Field(default=None, description="Issuing regulator or ministry")
    stage: ProposedStage = Field(..., description="Stage of the proposal (proposed, draft, consultation)")
    comment_deadline: Optional[datetime] = Field(default=None, description="Public comment deadline, if given")
    effective_date_estimate: Optional[datetime] = Field(default=None, description="Estimated effective date if reported")
    important_dates: List[ImportantDate] = Field(
        default_factory=list,
        description="Timeline of important dates including consultation periods and deadlines"
    )
    proposal_note: Optional[str] = Field(default=None, description="Short free-text summary of the proposal contents")


class LegislationDetails(BaseModel):
    """Legislative item and its current stage."""

    model_config = ConfigDict(
        title="LegislationDetails",
        json_schema_extra={
            "description": "Details for bills/acts including stage and any known effective date."
        }
    )

    bill_or_act_name: str = Field(..., description="Official or common name of the bill/act")
    stage: LegislationStage = Field(..., description="Current stage (introduced, passed, signed, vetoed)")
    jurisdiction_level: Optional[JurisdictionLevel] = Field(
        default=None,
        description="Administrative level (national, state, local)"
    )
    effective_date: Optional[datetime] = Field(default=None, description="Effective date if specified")
    important_dates: List[ImportantDate] = Field(
        default_factory=list,
        description="Timeline of important dates including votes, hearings, and implementation"
    )
    summary_note: Optional[str] = Field(default=None, description="Brief summary of the legislative content")


class CourtEnforcementDetails(BaseModel):
    """Court ruling or enforcement action and its outcome."""

    model_config = ConfigDict(
        title="CourtEnforcementDetails",
        json_schema_extra={
            "description": "Details for court or enforcement items including outcome and brief notes."
        }
    )

    authority: str = Field(..., description="Court or enforcing authority (e.g., High Court, EPA)")
    outcome: Outcome = Field(..., description="Outcome (upheld, struck, remanded, penalized, investigation_opened)")
    decision_date: Optional[datetime] = Field(default=None, description="Primary decision or action date, if reported")
    important_dates: List[ImportantDate] = Field(
        default_factory=list,
        description="Timeline of important dates including future deadlines"
    )
    penalties_note: Optional[str] = Field(default=None, description="Brief description of penalties or sanctions, if any")
    impacted_policy_note: Optional[str] = Field(default=None, description="Short note on impacted policy or regulation")


class GuidanceStandardDetails(BaseModel):
    """Non-binding guidance or standards updates."""

    model_config = ConfigDict(
        title="GuidanceStandardDetails",
        json_schema_extra={
            "description": "Details for guidance/standards including key changes and applicability date."
        }
    )

    issuing_body: str = Field(..., description="Body issuing the guidance/standard (e.g., ISO, EC)")
    document_name: str = Field(..., description="Name or identifier of the document")
    applicability_date: Optional[datetime] = Field(
        default=None,
        description="Date when guidance/standard becomes applicable, if given"
    )
    key_changes: List[str] = Field(default_factory=list, description="Short list of key changes")
    scope_note: Optional[str] = Field(default=None, description="Brief note on who/what this applies to")


class MarketAuctionDetails(BaseModel):
    """Results or highlights from a carbon market/auction."""

    model_config = ConfigDict(
        title="MarketAuctionDetails",
        json_schema_extra={
            "description": "Details for market or auction results including date and clearing price if given."
        }
    )

    market: str = Field(..., description='Market name (e.g., "EU ETS", "RGGI")')
    date: datetime = Field(..., description="Date of the market event or auction")
    clearing_price: Optional[Money] = Field(default=None, description="Clearing price if reported")
    volume_note: Optional[str] = Field(default=None, description="Brief note on volumes or coverage if mentioned")
    price_trend_note: Optional[str] = Field(default=None, description="Short note on price trend or comparison")


class CorporateDisclosureDetails(BaseModel):
    """Corporate filing or target announcement with basic context."""

    model_config = ConfigDict(
        title="CorporateDisclosureDetails",
        json_schema_extra={
            "description": "Details for corporate disclosure/target items with brief context notes."
        }
    )

    company: str = Field(..., description="Company name")
    framework: Optional[Framework] = Field(
        default=None,
        description="Relevant framework if clearly stated (e.g., CSRD, SEC)"
    )
    disclosure_or_target_note: str = Field(..., description="Short note describing the disclosure or target")
    period_note: Optional[str] = Field(default=None, description="Reporting or target period, if stated")
    assurance_note: Optional[str] = Field(default=None, description="Assurance/verification note if mentioned")


class FundingIncentiveDetails(BaseModel):
    """Public funding or incentive program announcement."""

    model_config = ConfigDict(
        title="FundingIncentiveDetails",
        json_schema_extra={
            "description": "Details for funding/incentive programs including type and deadlines."
        }
    )

    program_name: str = Field(..., description="Program name")
    agency: str = Field(..., description="Administering agency or ministry")
    incentive_type: IncentiveType = Field(..., description="Type of incentive (grant, tax_credit, rebate, loan, tender)")
    budget_note: Optional[str] = Field(default=None, description="Brief note on budget size or allocation")
    application_deadline: Optional[datetime] = Field(default=None, description="Application deadline if provided")


class EventDetails(BaseModel):
    """Upcoming event, hearing, consultation, or meeting."""

    model_config = ConfigDict(
        title="EventDetails",
        json_schema_extra={
            "description": "Details for events including dates, location, and registration link."
        }
    )

    event_name: str = Field(..., description="Event or meeting name")
    start_date: datetime = Field(..., description="Start date/time")
    end_date: Optional[datetime] = Field(default=None, description="End date/time if multi-day or specified")
    location: Optional[str] = Field(default=None, description="City/venue or virtual indicator if mentioned")
    registration_url: Optional[AnyUrl] = Field(default=None, description="Registration or information URL")


class ResearchReportDetails(BaseModel):
    """Research, analysis, or report release with key findings."""

    model_config = ConfigDict(
        title="ResearchReportDetails",
        json_schema_extra={
            "description": "Details for research/report items including headline findings and link."
        }
    )

    publisher: str = Field(..., description="Publisher or organization (e.g., IEA, NGO)")
    report_title: str = Field(..., description="Report title")
    release_date: Optional[datetime] = Field(default=None, description="Release/publication date if reported")
    headline_findings: List[str] = Field(default_factory=list, description="Short list of headline findings")
    report_url: Optional[AnyUrl] = Field(default=None, description="URL to the report if available")


# Union type for details (single nesting level only)
Details = Union[
    RegulatoryUpdateDetails,
    ProposedRuleDetails,
    LegislationDetails,
    CourtEnforcementDetails,
    GuidanceStandardDetails,
    MarketAuctionDetails,
    CorporateDisclosureDetails,
    FundingIncentiveDetails,
    EventDetails,
    ResearchReportDetails,
]
