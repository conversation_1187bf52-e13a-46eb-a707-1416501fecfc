"""
Unit tests for the smart news collector service.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from app.services.smart_collector import SmartCollectorAgent, SmartCollectorTools
from app.models import (
    SmartSource, SmartSourceType, ToolType, SmartCollectionPlan,
    SmartCollectionResult, WebSearchResult, ContentExtractionResult,
    URLExtractionResult, RSSParseResult, ArticleFilterResult
)


class TestSmartCollectorTools:
    """Test cases for SmartCollectorTools."""

    def test_init(self):
        """Test tools initialization."""
        mock_tavily = Mock()
        mock_logger = Mock()

        tools = SmartCollectorTools(mock_tavily, mock_logger)

        assert tools.tavily_client == mock_tavily
        assert tools.logger == mock_logger

    def test_search_web_success(self):
        """Test successful web search."""
        mock_tavily = Mock()
        mock_logger = Mock()

        mock_tavily.search.return_value = {
            "results": [
                {"title": "Test Article", "url": "https://example.com/test", "content": "Test content"}
            ]
        }

        tools = SmartCollectorTools(mock_tavily, mock_logger)
        result = tools.search_web("carbon regulations", time_range="day", max_results=5)

        assert isinstance(result, WebSearchResult)
        assert result.query == "carbon regulations"
        assert result.total_results == 1
        assert len(result.results) == 1
        assert result.results[0]["title"] == "Test Article"

        mock_tavily.search.assert_called_once_with(
            query="carbon regulations",
            topic="news",
            search_depth="basic",
            time_range="day",
            max_results=5
        )

    def test_search_web_failure(self):
        """Test web search failure handling."""
        mock_tavily = Mock()
        mock_logger = Mock()

        mock_tavily.search.side_effect = Exception("Search failed")

        tools = SmartCollectorTools(mock_tavily, mock_logger)
        result = tools.search_web("carbon regulations")

        assert isinstance(result, WebSearchResult)
        assert result.query == "carbon regulations"
        assert result.total_results == 0
        assert len(result.results) == 0

        mock_logger.error.assert_called_once()

    def test_extract_content_success(self):
        """Test successful content extraction."""
        mock_tavily = Mock()
        mock_logger = Mock()

        mock_tavily.extract.return_value = {
            "results": [
                {"content": "Test article content\nThis is a test article about carbon regulations."}
            ]
        }

        tools = SmartCollectorTools(mock_tavily, mock_logger)
        result = tools.extract_content("https://example.com/test")

        assert isinstance(result, ContentExtractionResult)
        assert result.url == "https://example.com/test"
        assert "carbon regulations" in result.content
        assert result.title == "Test article content"  # First line becomes title

        mock_tavily.extract.assert_called_once_with(urls=["https://example.com/test"])

    def test_extract_content_failure(self):
        """Test content extraction failure handling."""
        mock_tavily = Mock()
        mock_logger = Mock()

        mock_tavily.extract.side_effect = Exception("Extraction failed")

        tools = SmartCollectorTools(mock_tavily, mock_logger)
        result = tools.extract_content("https://example.com/test")

        assert isinstance(result, ContentExtractionResult)
        assert result.url == "https://example.com/test"
        assert result.content == ""
        assert result.title is None

        mock_logger.error.assert_called_once()

    @patch('app.services.smart_collector.feedparser')
    def test_parse_rss_success(self, mock_feedparser):
        """Test successful RSS parsing."""
        mock_tavily = Mock()
        mock_logger = Mock()

        # Mock feedparser response
        mock_feed = Mock()
        mock_feed.feed.title = "Test RSS Feed"
        mock_feed.entries = [
            Mock(
                title="RSS Article 1",
                link="https://example.com/rss1",
                summary="RSS summary 1",
                published="2024-01-01",
                published_parsed=None
            ),
            Mock(
                title="RSS Article 2",
                link="https://example.com/rss2",
                summary="RSS summary 2",
                published="2024-01-02",
                published_parsed=None
            )
        ]
        mock_feedparser.parse.return_value = mock_feed

        tools = SmartCollectorTools(mock_tavily, mock_logger)
        result = tools.parse_rss("https://example.com/rss", max_entries=10)

        assert isinstance(result, RSSParseResult)
        assert result.feed_url == "https://example.com/rss"
        assert result.feed_title == "Test RSS Feed"
        assert result.total_entries == 2
        assert len(result.entries) == 2
        assert result.entries[0]['title'] == "RSS Article 1"
        assert result.entries[0]['link'] == "https://example.com/rss1"

        mock_feedparser.parse.assert_called_once_with("https://example.com/rss")

    @patch('app.services.smart_collector.feedparser')
    def test_parse_rss_failure(self, mock_feedparser):
        """Test RSS parsing failure handling."""
        mock_tavily = Mock()
        mock_logger = Mock()

        mock_feedparser.parse.side_effect = Exception("RSS parsing failed")

        tools = SmartCollectorTools(mock_tavily, mock_logger)
        result = tools.parse_rss("https://example.com/rss")

        assert isinstance(result, RSSParseResult)
        assert result.feed_url == "https://example.com/rss"
        assert result.feed_title is None
        assert result.total_entries == 0
        assert len(result.entries) == 0

        mock_logger.error.assert_called_once()

    def test_filter_articles_success(self):
        """Test successful article filtering."""
        mock_tavily = Mock()
        mock_logger = Mock()

        articles = [
            {"title": "Carbon Tax News", "content": "Article about carbon tax policy", "summary": ""},
            {"title": "Sports News", "content": "Article about football", "summary": ""},
            {"title": "Climate Policy", "content": "Article about climate regulations", "summary": ""},
        ]

        tools = SmartCollectorTools(mock_tavily, mock_logger)
        result = tools.filter_articles(articles, "carbon climate")

        assert isinstance(result, ArticleFilterResult)
        assert result.original_count == 3
        assert result.filtered_count == 2  # Should match carbon tax and climate policy articles
        assert result.filter_criteria == "carbon climate"
        assert len(result.filtered_articles) == 2

        # Check that the right articles were filtered
        filtered_titles = [article['title'] for article in result.filtered_articles]
        assert "Carbon Tax News" in filtered_titles
        assert "Climate Policy" in filtered_titles
        assert "Sports News" not in filtered_titles


class TestSmartCollectorAgent:
    """Test cases for SmartCollectorAgent."""

    @patch('app.services.smart_collector.Agent')
    def test_init(self, mock_agent):
        """Test agent initialization."""
        mock_tavily = Mock()

        with patch('app.services.smart_collector.get_settings') as mock_settings:
            mock_settings.return_value.openrouter_api_key = "test-key"
            mock_settings.return_value.news_collector.smart_agent_model = "test-model"
            mock_settings.return_value.news_collector.smart_agent_temperature = 0.5

            agent = SmartCollectorAgent(mock_tavily)

            assert agent.tavily_client == mock_tavily
            assert agent.tools is not None
            assert agent.planning_agent is not None
            mock_agent.assert_called_once()

    def test_init_missing_api_key(self):
        """Test initialization with missing API key."""
        mock_tavily = Mock()

        with patch('app.services.smart_collector.get_settings') as mock_settings:
            mock_settings.return_value.openrouter_api_key = None

            with pytest.raises(ValueError, match="OPENROUTER_API_KEY is required"):
                SmartCollectorAgent(mock_tavily)

    @patch('app.services.smart_collector.Agent')
    def test_collect_from_smart_source_success(self, mock_agent):
        """Test successful smart source collection."""
        mock_tavily = Mock()

        with patch('app.services.smart_collector.get_settings') as mock_settings:
            mock_settings.return_value.openrouter_api_key = "test-key"
            mock_settings.return_value.news_collector.smart_agent_model = "test-model"
            mock_settings.return_value.news_collector.smart_agent_temperature = 0.5
            mock_settings.return_value.news_collector.max_articles_per_source = 10

            # Mock planning agent
            mock_planning_agent = Mock()
            mock_plan = SmartCollectionPlan(
                source_name="Test Source",
                reasoning="Test reasoning",
                tools_to_use=[ToolType.SEARCH_WEB],
                tool_parameters={"search_web": {"query": "test query"}},
                expected_outcome="Test outcome"
            )
            mock_planning_agent.run_sync.return_value.output = mock_plan
            mock_agent.return_value = mock_planning_agent

            # Create smart source
            smart_source = SmartSource(
                name="Test Source",
                type=SmartSourceType.WEB_SEARCH,
                query="carbon regulations",
                instructions="Test instructions",
                available_tools=[ToolType.SEARCH_WEB],
                max_articles=5
            )

            agent = SmartCollectorAgent(mock_tavily)

            # Mock tool execution
            with patch.object(agent, '_execute_tool') as mock_execute:
                mock_execution = Mock()
                mock_execution.success = True
                mock_execution.tool_type = ToolType.SEARCH_WEB
                mock_execution.result = WebSearchResult(
                    query="test query",
                    results=[{"title": "Test", "url": "https://test.com", "content": "Test content"}],
                    total_results=1
                )
                mock_execute.return_value = mock_execution

                with patch.object(agent, '_convert_tool_result_to_articles') as mock_convert:
                    from app.services.news_collector import CollectedArticle
                    mock_convert.return_value = [
                        CollectedArticle(
                            title="Test Article",
                            url="https://test.com",
                            content="Test content",
                            source_name="Test Source"
                        )
                    ]

                    result = agent.collect_from_smart_source(smart_source)

                    assert isinstance(result, SmartCollectionResult)
                    assert result.success is True
                    assert result.source_name == "Test Source"
                    assert result.articles_found == 1
                    assert len(result.tool_executions) == 1

    @patch('app.services.smart_collector.Agent')
    def test_collect_from_smart_source_failure(self, mock_agent):
        """Test smart source collection failure handling."""
        mock_tavily = Mock()

        with patch('app.services.smart_collector.get_settings') as mock_settings:
            mock_settings.return_value.openrouter_api_key = "test-key"
            mock_settings.return_value.news_collector.smart_agent_model = "test-model"
            mock_settings.return_value.news_collector.smart_agent_temperature = 0.5

            # Mock planning agent that fails
            mock_planning_agent = Mock()
            mock_planning_agent.run_sync.side_effect = Exception("Planning failed")
            mock_agent.return_value = mock_planning_agent

            smart_source = SmartSource(
                name="Test Source",
                type=SmartSourceType.WEB_SEARCH,
                query="carbon regulations",
                instructions="Test instructions",
                available_tools=[ToolType.SEARCH_WEB]
            )

            agent = SmartCollectorAgent(mock_tavily)
            result = agent.collect_from_smart_source(smart_source)

            assert isinstance(result, SmartCollectionResult)
            assert result.success is False
            assert result.source_name == "Test Source"
            assert result.articles_found == 0
            assert "Planning failed" in result.error

    @patch('app.services.smart_collector.Agent')
    def test_create_fallback_plan(self, mock_agent):
        """Test fallback plan creation."""
        mock_tavily = Mock()

        with patch('app.services.smart_collector.get_settings') as mock_settings:
            mock_settings.return_value.openrouter_api_key = "test-key"
            mock_settings.return_value.news_collector.smart_agent_model = "test-model"
            mock_settings.return_value.news_collector.smart_agent_temperature = 0.5

            agent = SmartCollectorAgent(mock_tavily)

            # Test web search fallback
            web_source = SmartSource(
                name="Web Source",
                type=SmartSourceType.WEB_SEARCH,
                query="carbon regulations",
                instructions="Test instructions",
                available_tools=[ToolType.SEARCH_WEB]
            )

            plan = agent._create_fallback_plan(web_source)
            assert isinstance(plan, SmartCollectionPlan)
            assert plan.source_name == "Web Source"
            assert ToolType.SEARCH_WEB in plan.tools_to_use
            assert "search_web" in plan.tool_parameters

            # Test RSS fallback
            rss_source = SmartSource(
                name="RSS Source",
                type=SmartSourceType.RSS_FEED,
                url="https://example.com/rss",
                instructions="Test instructions",
                available_tools=[ToolType.PARSE_RSS]
            )

            plan = agent._create_fallback_plan(rss_source)
            assert isinstance(plan, SmartCollectionPlan)
            assert plan.source_name == "RSS Source"
            assert ToolType.PARSE_RSS in plan.tools_to_use
            assert "parse_rss" in plan.tool_parameters