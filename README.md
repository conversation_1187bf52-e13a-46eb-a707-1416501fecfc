# Carbon Regulation News Platform

An automated system for collecting, analyzing, and summarizing carbon regulation news using AI. The platform runs a daily pipeline that transforms raw news into structured intelligence reports.

## How It Works

The system operates in four main phases:

```mermaid
graph LR
    A[News Collection] --> B[AI Processing]
    B --> C[Daily Summary] --> D[API/Notifications]
```

### Phase 1: News Collection
The system collects news from multiple sources:
- **Web Search**: Uses Tavily API with configurable search queries
- **Direct Sources**: Monitors specific URLs (Reuters, Bloomberg, etc.)
- **Deduplication**: Removes duplicates based on URL and content similarity

```mermaid
graph TD
    A[Search Queries] --> C[Tavily API]
    B[Source URLs] --> C
    C --> D[Raw Articles]
    D --> E[Deduplication]
    E --> F[Stored Articles]
```

### Phase 2: AI Processing
Each article is processed through LLM to extract structured information:
- **Classification**: Categorizes by type (carbon pricing, emissions standards, etc.)
- **Key Points**: Extracts important facts, dates, and requirements
- **Summary**: Creates concise summaries for policy professionals

```mermaid
graph TD
    A[Raw Article] --> B[LLM Classification]
    A --> C[LLM Content Extraction]
    B --> D[Structured Data]
    C --> D
    D --> E[Database Storage]
```

### Phase 3: Daily Summary Generation
Processed articles are combined into daily intelligence reports:
- **Clustering**: Groups related articles by theme
- **Markdown Generation**: Creates formatted reports with source links
- **Statistics**: Provides metrics on coverage and trends

### Phase 4: Distribution
Results are made available through multiple channels:
- **Web Dashboard**: Interactive web interface for monitoring and browsing
- **REST API**: Programmatic access to articles and summaries
- **Notifications**: Webhook and Slack alerts for new summaries
- **Database**: Persistent storage for historical analysis

## Technical Implementation

### News Collection Service (`NewsCollectionService`)
The collection service handles multi-source news gathering:

```python
# Configurable search queries
search_queries = [
    "carbon regulations emission standards environmental policy",
    "clean energy regulations carbon policy sustainability",
    "carbon accounting standards disclosure reporting"
]

# Specific sources to monitor
specific_sources = [
    "https://www.reuters.com/sustainability/clean-energy/",
    "https://www.reuters.com/sustainability/climate-energy/"
]
```

**How it works:**
1. Executes search queries via Tavily API
2. Extracts article URLs using AI-powered filtering
3. Downloads and processes article content
4. Removes duplicates based on URL normalization
5. Stores articles in database with metadata

### AI Processing Service (`AINewsParsingService`)
Uses LLM via OpenRouter to analyze each article sequentially:

```mermaid
graph TD
    A[Article Content] --> B[Classification Agent]
    B --> C[Content Extraction Agent]
    C --> D[Structured JSON]
    D --> E[Database Storage]
```

**Processing Steps:**
1. **Classification Agent**: Determines article category, type, and affected jurisdictions
2. **Content Agent**: Extracts title, summary, and key points
3. **Summary Agent**: Generates daily summaries from multiple articles (separate task)

### Task Scheduler Service (`TaskSchedulerService`)
Orchestrates the entire pipeline sequentially:

```mermaid
graph TD
    A[Daily Schedule 09:00] --> B[News Collection]
    B --> C[AI Processing]
    C --> D[Daily Summary]
    D --> E[Notifications]
    F[Manual Trigger] --> G[Any Task Type]
    G --> B
```

**Task Types:**
- `NEWS_COLLECTION`: Collect new articles
- `AI_PROCESSING`: Process unprocessed articles
- `DAILY_SUMMARY`: Generate daily summary
- `FULL_PIPELINE`: Run collection → processing → summary sequentially

## Database Structure

The system uses SQLAlchemy with four main models:

```mermaid
erDiagram
    NewsArticle ||--o{ TaskResult : "many-to-many"
    TaskExecution ||--o{ TaskResult : "one-to-many"
    TaskExecution ||--o{ NotificationLog : "one-to-many"

    NewsArticle {
        int id
        string title
        string url
        text content
        string source_name
        datetime published_date
        datetime collected_at
        bool is_processed
        datetime processed_at
        json ai_classification
        text ai_summary
        json ai_key_points
        json ai_details
    }

    TaskExecution {
        int id
        string task_name
        string task_type
        string status
        datetime started_at
        datetime completed_at
        float duration_seconds
        json result_summary
        text error_message
        json config_snapshot
    }

    TaskResult {
        int id
        int task_execution_id
        string result_type
        string title
        text summary
        json key_findings
        json statistics
        json result_metadata
        datetime created_at
    }

    NotificationLog {
        int id
        int task_execution_id
        string notification_type
        string recipient
        string status
        datetime sent_at
        int retry_count
    }
```

## Configuration

The system uses both YAML configuration and environment variables:

### config.yaml (Main Configuration)
The system uses a comprehensive YAML configuration file with extensive customization options:

```yaml
# Environment and API settings
environment: production
debug: false
log_level: INFO

# Database configuration
database:
  url: sqlite:///./data/carbon_news.db
  echo: false

# Web API settings
api:
  host: 0.0.0.0
  port: 8000
  debug: false

# News collection configuration
news_collector:
  max_articles_per_source: 5
  default_time_range: day
  search_queries:
    - "carbon regulations emission standards environmental policy"
    - "clean energy regulations carbon policy sustainability"
    - "carbon accounting standards disclosure reporting"
    - "carbon pricing markets ETS carbon tax"
    - "net zero commitments corporate sustainability"
    - "renewable energy mandates policy"
  specific_sources:
    - "https://www.reuters.com/sustainability/clean-energy/"
    - "https://www.reuters.com/sustainability/climate-energy/"
  rss_feeds:
    - "https://www.carbonbrief.org/feed/"
  search_settings:
    include_domains: []
    exclude_domains:
      - twitter.com
      - facebook.com
    search_depth: basic
    extract_full_content: true
    min_content_length: 100

# AI processing models and settings
ai_processing:
  models:
    news_parser:
      model_name: "openai/gpt-4.1-mini"
      temperature: 0.3
    url_extractor:
      model_name: "openai/gpt-4.1-mini"
      temperature: 0.3
    classifier:
      model_name: "openai/gpt-4.1-mini"
      temperature: 0.3
    summarizer:
      model_name: "openai/gpt-4.1-mini"
      temperature: 0.7

# Task scheduling
scheduler:
  daily_run_time: "09:00"
  max_task_history: 100
  task_timeout_minutes: 30
  enable_auto_scheduling: true
  retry_settings:
    max_retries: 3
    retry_delay_minutes: 5

# Notification settings
notifications:
  enable_notifications: true
  webhook_url: null
  slack_webhook_url: null
  email:
    enabled: false
    smtp_server: null
    smtp_port: 587
    username: null
    password: null
    from_address: null
    to_addresses: []
  triggers:
    on_collection_complete: true
    on_error: true
    on_high_priority_articles: false
    min_articles_threshold: 5

# Monitoring and health checks
monitoring:
  health_check:
    enabled: true
    check_interval_minutes: 5
  performance:
    enabled: true
    retention_days: 30
  alerts:
    max_collection_time_minutes: 60
    max_error_rate_percent: 10
    max_days_without_articles: 2

# AI prompts configuration (extensive prompt templates)
prompts:
  ai_parser:
    classification_system_prompt: |
      You are an expert at classifying carbon regulation and climate policy news...
      # [Detailed classification categories and instructions - 200+ lines]
    content_extraction_system_prompt: |
      You are an expert at extracting and summarizing news content...
    # [Additional prompt templates for clustering, summarization, etc.]
```

> **Note**: The config.yaml file contains much more extensive configuration including detailed AI prompts (500+ lines), classification categories, and advanced settings. The above shows key sections - see the full file for complete options.

### Environment Variables (API Keys)
```bash
# Required API Keys (security - not in YAML)
TAVILY_API_KEY=your_tavily_key
OPENROUTER_API_KEY=your_openrouter_key
```

## Web Dashboard

The platform includes a comprehensive web dashboard built with FastAPI and Jinja2 templates, providing an intuitive interface for monitoring and managing the system.

### Dashboard Features

**Main Dashboard (`/dashboard/`)**
- Recent task execution status
- Latest daily summary preview with markdown rendering
- Quick access to all system components

**Articles Management (`/dashboard/articles`)**
- Browse and search collected articles
- Filter by processing status, source, and date
- Detailed article view with AI analysis results
- Full content display with structured metadata

**Daily Summaries (`/dashboard/summaries`)**
- Browse historical daily summaries
- Markdown-rendered summaries with embedded source links
- Pagination and search functionality
- Export and sharing capabilities

**Task Monitoring (`/dashboard/tasks`)**
- Detailed task logs and error reporting
- Task result visualization
- Manual task triggering interface

### Dashboard Technology
- **Frontend**: Clean, responsive design using Tailwind CSS
- **Templates**: Jinja2 templating with markdown rendering
- **Navigation**: Intuitive navigation between all system components


## API Structure

The FastAPI-based REST API provides programmatic access to all functionality:

```mermaid
graph TD
    A[Root /] --> B[API Info]
    C[/health] --> D[System Status]
    C --> E[/health/statistics]
    C --> F[/health/database]
    C --> G[/health/scheduler]

    H[/tasks] --> I[/tasks/executions]
    H --> J[/tasks/trigger]
    H --> K[/tasks/results]
    H --> L[/tasks/types]
    H --> M[/tasks/recent]

    N[/news] --> O[/news/articles]
    N --> P[/news/articles/{id}]
    N --> Q[/news/articles/{id}/content]
    N --> R[/news/summaries]

    S[/ping] --> T[Load Balancer Check]
```

**Complete Endpoint List:**

**Root & Health:**
- `GET /` - API information and endpoints
- `GET /ping` - Simple health check for load balancers
- `GET /health` - System health and statistics
- `GET /health/statistics` - Detailed performance metrics
- `GET /health/database` - Database connectivity check
- `GET /health/scheduler` - Scheduler status

**Web Dashboard:**
- `GET /dashboard/` - Main dashboard interface
- `GET /dashboard/articles` - Articles management interface
- `GET /dashboard/articles/{id}` - Detailed article view
- `GET /dashboard/summaries` - Daily summaries interface
- `GET /dashboard/summaries/{id}` - Detailed summary view
- `GET /dashboard/tasks` - Task monitoring interface
- `GET /dashboard/tasks/{id}` - Detailed task execution view

**Task Management:**
- `GET /tasks/executions` - Task execution history with filtering
- `GET /tasks/executions/{id}` - Specific task execution details
- `POST /tasks/trigger` - Manually trigger any task type
- `GET /tasks/results` - Task results with filtering
- `GET /tasks/types` - Available task types
- `GET /tasks/recent` - Recent task activity

**News & Content:**
- `GET /news/articles` - Articles with filtering/pagination
- `GET /news/articles/{id}` - Specific article details
- `GET /news/articles/{id}/content` - Full article content
- `GET /news/summaries` - Daily summaries with pagination

## Project Structure

```
app/
├── core/                    # Foundation components
│   ├── config.py           # Pydantic settings management
│   ├── database.py         # SQLAlchemy setup
│   ├── models.py           # Database models
│   └── logging.py          # Structured logging
├── services/               # Business logic
│   ├── news_collector.py   # Multi-source news collection
│   ├── ai_parser.py        # LLM content analysis
│   ├── scheduler.py        # Task orchestration
│   └── notifications.py    # Multi-channel notifications
├── api/                    # REST API layer
│   ├── main.py            # FastAPI application
│   ├── schemas.py         # API data models
│   └── routes/            # Endpoint implementations
├── web/                    # Web dashboard
│   ├── routes.py          # Dashboard route handlers
│   └── templates/         # Jinja2 HTML templates
│       ├── base.html      # Base template with navigation
│       ├── dashboard.html # Main dashboard page
│       ├── articles.html  # Articles listing and detail
│       ├── summaries.html # Daily summaries interface
│       └── tasks.html     # Task monitoring interface
└── scripts/               # Operational utilities
    └── run_daily_task.py  # Manual task execution

tests/                     # Comprehensive test suite
├── test_services/         # Service unit tests
├── test_api/             # API integration tests
└── test_integration/     # End-to-end tests
```

## Key Technologies

- **Backend**: Python 3.12+ with FastAPI
- **Database**: SQLAlchemy ORM (SQLite/PostgreSQL)
- **AI Processing**: LLM via OpenRouter (configurable model)
- **Web Scraping**: Tavily API for content extraction
- **Task Scheduling**: Built-in scheduler with cron-like functionality
- **Testing**: Pytest with comprehensive coverage

## How It All Works Together

### Daily Workflow
1. **09:00 Daily Trigger**: Scheduler automatically starts the full pipeline
2. **Collection Phase**: System searches for new articles using configured queries
3. **Processing Phase**: LLM analyzes each article for classification and key points
4. **Summary Phase**: LLM generates a comprehensive daily summary with clustering
5. **Distribution Phase**: Results are stored in database and notifications sent

### Manual Operations
- **Web Dashboard**: Interactive interface for monitoring, browsing, and managing the system
- **API Access**: Real-time access to articles, summaries, and system status
- **Manual Triggers**: Force execution of any pipeline phase via API or dashboard
- **Monitoring**: Health checks and performance metrics available via endpoints and dashboard
- **Configuration**: Environment variables and YAML configuration control all system behavior

### Data Flow
```mermaid
graph TD
    A[Search Queries] --> B[Tavily API]
    B --> C[Raw Articles]
    C --> D[Database Storage]
    D --> E[LLM Processing]
    E --> F[Structured Data]
    F --> G[Daily Summary]
    G --> H[API/Notifications]
```

### Error Handling
- **Retry Logic**: Automatic retries for failed operations
- **Graceful Degradation**: System continues with partial failures
- **Comprehensive Logging**: All operations logged with correlation IDs
- **Health Monitoring**: Continuous health checks with status reporting

This architecture ensures reliable, automated processing of carbon regulation news while providing flexible access to results through a comprehensive API.

## Monitoring & Observability

The platform includes comprehensive monitoring capabilities designed for production environments with enterprise-grade observability requirements.

### Performance Monitoring
- **Real-time Metrics**: Task execution timing, throughput, and resource utilization
- **Health Checks**: Multi-level health verification with dependency checking
- **Performance Baselines**: Automated performance regression detection
- **Resource Tracking**: Memory usage, database connections, and API rate limits
- **SLA Monitoring**: Service level agreement compliance tracking

### Logging Architecture
- **Structured Logging**: JSON-formatted logs with consistent schema
- **Correlation IDs**: Request tracing across service boundaries
- **Log Levels**: Configurable verbosity with production-optimized defaults
- **Error Context**: Detailed error information with stack traces and context
- **Audit Trail**: Complete operational history for compliance requirements

### Alerting & Notifications
- **Threshold-Based Alerts**: Configurable performance and error rate thresholds
- **Anomaly Detection**: Statistical analysis for unusual pattern identification
- **Escalation Policies**: Multi-tier notification with severity-based routing
- **Integration Points**: Webhook and Slack integration for operations teams
- **Dashboard Integration**: Metrics export for external monitoring systems

## Technical Implementation

### AI Processing Pipeline
The platform leverages state-of-the-art language models through a sophisticated processing pipeline optimized for regulatory content analysis.

**Model Architecture:**
- **Primary Model**: OpenAI GPT-4.1 via OpenRouter
- **Specialized Prompts**: Domain-specific prompts engineered for regulatory content
- **Multi-Agent System**: Separate agents for classification, extraction, and summarization
- **Output Validation**: JSON schema validation with fallback error handling
- **Performance Optimization**: Model response caching and connection pooling

**Processing Stages:**
1. **Content Preprocessing**: Text cleaning, normalization, and structure detection
2. **Classification**: Multi-dimensional categorization (type, jurisdiction, sector)
3. **Information Extraction**: Key facts, dates, requirements, and stakeholder identification
4. **Summary Generation**: Executive summaries optimized for policy professionals
5. **Quality Assurance**: Automated validation and human-readable output verification

### Data Collection Technology
The platform employs advanced web scraping and content extraction technologies designed for reliability and scale.

**Collection Infrastructure:**
- **Tavily API Integration**: Professional-grade web search with content extraction
- **Custom Scrapers**: Specialized extractors for regulatory websites and RSS feeds
- **Content Validation**: AI-powered URL filtering and content quality assessment
- **Deduplication Engine**: Multi-level duplicate detection using URL and content hashing
- **Rate Limiting**: Respectful crawling with configurable delays and retry logic

**Source Management:**
- **Dynamic Source Discovery**: Automatic identification of new relevant sources
- **Source Credibility Scoring**: Automated assessment of source reliability and authority
- **Content Freshness Tracking**: Timestamp-based content update detection
- **Geographic Targeting**: Region-specific source prioritization and filtering

## Scalability & Performance

### Production Readiness
- **Error Recovery**: Comprehensive error handling with automatic retry mechanisms
- **Graceful Degradation**: Service continues operation with reduced functionality during failures
- **Health Monitoring**: Continuous health checks with automatic service restart
- **Configuration Management**: Environment-specific configuration with validation
- **Security Hardening**: Input validation, SQL injection prevention, and secure API design

## Testing & Quality Assurance

The platform includes a comprehensive testing suite designed to ensure reliability and maintainability across all components.

### Test Coverage
- **Unit Tests**: Individual service and component testing with 90%+ coverage
- **Integration Tests**: End-to-end pipeline testing with real data scenarios
- **API Tests**: Complete REST API endpoint validation and error handling
- **Performance Tests**: Load testing and performance regression detection
- **Mock Testing**: External API mocking for reliable test execution

### Testing Architecture
- **Pytest Framework**: Modern Python testing with fixtures and parameterization
- **Database Testing**: Isolated test databases with automatic cleanup
- **API Testing**: FastAPI test client integration with request/response validation
- **Mock Services**: Comprehensive mocking of external dependencies (Tavily, OpenRouter)
- **Continuous Integration**: Automated testing on code changes with GitHub Actions

### Quality Metrics
- **Code Coverage**: Automated coverage reporting with threshold enforcement
- **Performance Benchmarks**: Automated performance testing with regression detection
- **Error Rate Monitoring**: Production error tracking with alerting thresholds
- **API Response Time**: Endpoint performance monitoring and optimization
- **Data Quality Validation**: Automated validation of AI processing results


## Technical Specifications

### Technology Stack
- **Backend Framework**: FastAPI with Python 3.12+
- **Database**: SQLAlchemy ORM
- **AI Processing**: LLMs via OpenRouter
- **Web Scraping**: Tavily API integration with custom content extraction capabilities
- **Task Scheduling**: Schedule library for automated task execution
- **Testing**: Pytest framework with comprehensive unit and integration test coverage

### External Dependencies
- **Tavily API**: Professional web search and content extraction service
- **OpenRouter**: AI model access

### Deployment Requirements
- **Python Runtime**: 3.12+ with virtual environment support
- **Database**: SQLite for development, PostgreSQL recommended for production


## Implementation Highlights

### Security & Reliability
- **Input Validation**: Comprehensive validation of all user inputs and API parameters
- **Error Handling**: Structured error responses with detailed logging and recovery mechanisms
- **API Security**: Rate limiting, CORS configuration, and secure header management
- **Data Protection**: Secure handling of API keys and sensitive configuration data
- **Audit Logging**: Complete operational audit trail for compliance and debugging

## Project Structure

The codebase follows a clean, modular architecture with clear separation of concerns:

```
carbon-regulation-news/
├── app/
│   ├── core/                    # Foundation layer
│   │   ├── config.py           # Configuration management with Pydantic
│   │   ├── database.py         # SQLAlchemy setup and session management
│   │   ├── models.py           # Database models and relationships
│   │   └── logging.py          # Structured logging configuration
│   ├── services/               # Business logic layer
│   │   ├── news_collector.py   # Multi-source news aggregation
│   │   ├── ai_parser.py        # AI-powered content analysis
│   │   ├── scheduler.py        # Task orchestration and automation
│   │   └── notifications.py    # Multi-channel notification system
│   ├── api/                    # Presentation layer
│   │   ├── main.py            # FastAPI application and middleware
│   │   ├── schemas.py         # Pydantic models for API contracts
│   │   └── routes/            # REST endpoint implementations
│   └── scripts/               # Operational utilities
├── tests/                     # Comprehensive test suite
│   ├── test_services/         # Service layer unit tests
│   ├── test_api/             # API endpoint integration tests
│   └── test_integration/     # End-to-end pipeline tests
└── requirements.txt          # Production dependencies
```


