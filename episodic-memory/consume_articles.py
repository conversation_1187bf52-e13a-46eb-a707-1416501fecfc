import asyncio
import json
import logging
import os
import glob
from datetime import datetime, timezone
from logging import INFO

from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.gemini import <PERSON><PERSON>mbedder, GeminiEmbedderConfig
from graphiti_core.driver.kuzu_driver import KuzuDriver

# Configure logging
logging.basicConfig(
    level=INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)
logger = logging.getLogger(__name__)

load_dotenv()

# Environment variables
OPENAI_BASE_URL = os.environ.get('OPENROUTER_BASE_URL')
OPENAI_API_KEY = os.environ.get('OPENROUTER_API_KEY')
GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')
KUZU_DB_PATH = os.environ.get('KUZU_DB_PATH', 'episodic-memory/graph_db/kuzu.db')

# Initialize clients
llm_client = OpenAIGenericClient(
    config=LLMConfig(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_BASE_URL,
        model="openai/gpt-4.1-mini",
    )
)

embedder = GeminiEmbedder(
    config=GeminiEmbedderConfig(
        api_key=GEMINI_API_KEY,
        embedding_model="embedding-001"
    )
)

cross_encoder = OpenAIRerankerClient(
    config=LLMConfig(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_BASE_URL,
        model="openai/gpt-4.1-nano",
    )
)

kuzu_driver = KuzuDriver(db=KUZU_DB_PATH)


def load_articles_from_folder(folder_path):
    """Load all JSON articles from the specified folder."""
    articles = []
    json_files = glob.glob(os.path.join(folder_path, "*.json"))

    logger.info(f"Found {len(json_files)} JSON files in {folder_path}")

    for file_path in json_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                article = json.load(f)
                articles.append((article, os.path.basename(file_path)))
                logger.debug(f"Loaded article from {os.path.basename(file_path)}")
        except Exception as e:
            logger.error(f"Error loading {file_path}: {e}")

    return articles


async def consume_articles():
    """Load articles from the collected_articles folder and add them to the episodic memory."""

    # Initialize Graphiti
    graphiti = Graphiti(
        graph_driver=kuzu_driver,
        llm_client=llm_client,
        embedder=embedder,
        cross_encoder=cross_encoder,
    )

    try:
        # Initialize the graph database with graphiti's indices
        logger.info("Building indices and constraints...")
        await graphiti.build_indices_and_constraints()

        # Load articles from the folder
        articles_folder = "episodic-memory/collected_articles"
        articles = load_articles_from_folder(articles_folder)

        logger.info(f"Processing {len(articles)} articles...")

        # Process each article
        for i, (article, filename) in enumerate(articles):
            try:
                # Create episode name from filename
                episode_name = filename.replace('.json', '').replace('_', ' ').title()

                # Use the full article JSON as the episode content
                episode_content = json.dumps(article, indent=2)

                # Extract timestamp if available
                reference_time = datetime.now(timezone.utc)
                if article.get('published_date'):
                    try:
                        reference_time = datetime.fromisoformat(article['published_date'].replace('Z', '+00:00'))
                    except:
                        # If parsing fails, use collected_at or current time
                        if article.get('collected_at'):
                            try:
                                reference_time = datetime.fromisoformat(article['collected_at'].replace('Z', '+00:00'))
                            except:
                                pass

                # Add episode to the graph
                await graphiti.add_episode(
                    name=episode_name,
                    episode_body=episode_content,
                    source=EpisodeType.json,
                    source_description=f"Carbon regulation research article from {article.get('source_name', 'unknown source')}",
                    reference_time=reference_time,
                )

                logger.info(f"Added episode {i+1}/{len(articles)}: {episode_name}")

            except Exception as e:
                logger.error(f"Error processing article {filename}: {e}")
                continue

        logger.info(f"Successfully processed {len(articles)} articles into episodic memory")

    except Exception as e:
        logger.error(f"Error during article consumption: {e}")
        raise
    finally:
        # Close the connection
        await graphiti.close()
        logger.info("Connection closed")


async def main():
    """Main function to consume articles."""
    await consume_articles()


if __name__ == '__main__':
    asyncio.run(main())