#!/usr/bin/env python3
"""
News Extractor from Database

This script extracts processed news articles from the SQLite database
and saves them as individual JSON files in the collected_articles folder.
"""

import os
import sys
import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add the parent directory to the path so we can import from app
sys.path.append(str(Path(__file__).parent.parent))

def connect_to_database(db_path: str) -> sqlite3.Connection:
    """Connect to the SQLite database."""
    if not os.path.exists(db_path):
        raise FileNotFoundError(f"Database file not found: {db_path}")
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # This allows accessing columns by name
    return conn

def serialize_datetime(obj):
    """JSON serializer for datetime objects."""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def extract_articles_from_db(db_path: str) -> List[Dict[str, Any]]:
    """Extract all news articles from the database."""
    conn = connect_to_database(db_path)
    
    try:
        cursor = conn.cursor()
        
        # Query to get all news articles
        query = """
        SELECT 
            id,
            title,
            url,
            content,
            source_name,
            published_date,
            collected_at,
            is_processed,
            processed_at,
            ai_classification,
            ai_summary,
            ai_key_points,
            ai_details
        FROM news_articles
        ORDER BY collected_at DESC
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        articles = []
        for row in rows:
            article = {
                'id': row['id'],
                'title': row['title'],
                'url': row['url'],
                'content': row['content'],
                'source_name': row['source_name'],
                'published_date': row['published_date'],
                'collected_at': row['collected_at'],
                'is_processed': bool(row['is_processed']),
                'processed_at': row['processed_at'],
                'ai_classification': json.loads(row['ai_classification']) if row['ai_classification'] else None,
                'ai_summary': row['ai_summary'],
                'ai_key_points': json.loads(row['ai_key_points']) if row['ai_key_points'] else None,
                'ai_details': json.loads(row['ai_details']) if row['ai_details'] else None,
            }
            articles.append(article)
        
        return articles
    
    finally:
        conn.close()

def create_safe_filename(title: str, article_id: int) -> str:
    """Create a safe filename from article title and ID."""
    # Remove or replace problematic characters
    safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
    # Limit length and add ID to ensure uniqueness
    safe_title = safe_title[:50] if len(safe_title) > 50 else safe_title
    safe_title = safe_title.strip().replace(' ', '_')
    return f"article_{article_id}_{safe_title}.json"

def save_articles_as_json(articles: List[Dict[str, Any]], output_dir: str) -> None:
    """Save articles as individual JSON files."""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"Saving {len(articles)} articles to {output_path}")
    
    for article in articles:
        filename = create_safe_filename(article['title'], article['id'])
        file_path = output_path / filename
        
        # Convert datetime strings for JSON serialization
        article_copy = article.copy()
        for date_field in ['published_date', 'collected_at', 'processed_at']:
            if article_copy[date_field]:
                # Parse and convert to ISO format if it's a string
                if isinstance(article_copy[date_field], str):
                    try:
                        dt = datetime.fromisoformat(article_copy[date_field].replace('Z', '+00:00'))
                        article_copy[date_field] = dt.isoformat()
                    except:
                        pass  # Keep original if parsing fails
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(article_copy, f, indent=2, ensure_ascii=False, default=serialize_datetime)
        
        print(f"Saved: {filename}")

def main():
    """Main function to extract and save articles."""
    # Set up paths
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    db_path = project_root / "data" / "carbon_news.db"
    output_dir = script_dir / "collected_articles"
    
    print("=" * 60)
    print("NEWS ARTICLE EXTRACTOR")
    print("=" * 60)
    print(f"Database path: {db_path}")
    print(f"Output directory: {output_dir}")
    print()
    
    try:
        # Extract articles from database
        print("Extracting articles from database...")
        articles = extract_articles_from_db(str(db_path))
        
        if not articles:
            print("No articles found in the database.")
            return
        
        print(f"Found {len(articles)} articles.")
        
        # Show some statistics
        processed_count = sum(1 for article in articles if article['is_processed'])
        sources = set(article['source_name'] for article in articles)
        
        print(f"- Processed articles: {processed_count}")
        print(f"- Unprocessed articles: {len(articles) - processed_count}")
        print(f"- Unique sources: {len(sources)}")
        print(f"- Sources: {', '.join(sorted(sources))}")
        print()
        
        # Save articles as JSON files
        print("Saving articles as JSON files...")
        save_articles_as_json(articles, str(output_dir))
        
        print()
        print("✅ Extraction completed successfully!")
        print(f"All articles have been saved to: {output_dir}")
        
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        print("Make sure the database file exists and the path is correct.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
