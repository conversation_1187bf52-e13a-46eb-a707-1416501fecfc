import asyncio
import logging
import os
import sys
from datetime import datetime
from typing import Op<PERSON>

from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF
from graphiti_core.llm_client.openai_generic_client import OpenAIGenericClient
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
from graphiti_core.driver.kuzu_driver import KuzuDriver

# Configure logging
logging.basicConfig(
    level=logging.WARNING,  # Reduce noise in CLI
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
)
logger = logging.getLogger(__name__)

load_dotenv()

# Environment variables
OPENAI_BASE_URL = os.environ.get('OPENROUTER_BASE_URL')
OPENAI_API_KEY = os.environ.get('OPENROUTER_API_KEY')
GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')
KUZU_DB_PATH = os.environ.get('KUZU_DB_PATH', 'episodic-memory/graph_db/kuzu.db')

# Initialize clients
llm_client = OpenAIGenericClient(
    config=LLMConfig(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_BASE_URL,
        model="openai/gpt-4.1-mini",
    )
)

embedder = GeminiEmbedder(
    config=GeminiEmbedderConfig(
        api_key=GEMINI_API_KEY,
        embedding_model="embedding-001"
    )
)

cross_encoder = OpenAIRerankerClient(
    config=LLMConfig(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_BASE_URL,
        model="openai/gpt-4.1-nano",
    )
)

kuzu_driver = KuzuDriver(db=KUZU_DB_PATH)


class ArticleQueryCLI:
    """CLI tool for querying the episodic memory of carbon regulation articles."""

    def __init__(self):
        self.graphiti = None

    async def initialize(self):
        """Initialize the Graphiti connection."""
        self.graphiti = Graphiti(
            graph_driver=kuzu_driver,
            llm_client=llm_client,
            embedder=embedder,
            cross_encoder=cross_encoder,
        )
        # Build indices if needed (safe to call multiple times)
        await self.graphiti.build_indices_and_constraints()

    async def close(self):
        """Close the Graphiti connection."""
        if self.graphiti:
            await self.graphiti.close()

    def print_banner(self):
        """Print the CLI banner."""
        print("=" * 80)
        print("🧠 CARBON REGULATION RESEARCH - EPISODIC MEMORY QUERY TOOL")
        print("=" * 80)
        print("Ask questions about carbon regulation articles in your knowledge base.")
        print("Type 'help' for commands, 'quit' or 'exit' to leave.")
        print("-" * 80)

    def print_help(self):
        """Print help information."""
        print("\n📖 AVAILABLE COMMANDS:")
        print("  help          - Show this help message")
        print("  search <q>    - Search for relationships/facts (default mode)")
        print("  nodes <q>     - Search for specific entities/nodes")
        print("  detailed <q>  - Detailed search with reranking")
        print("  stats         - Show database statistics")
        print("  quit/exit     - Exit the application")
        print("\n💡 EXAMPLES:")
        print("  search LNG emissions")
        print("  nodes Rystad Energy")
        print("  detailed What are the key findings about marine LNG emissions?")
        print()

    async def search_relationships(self, query: str, limit: int = 5):
        """Search for relationships/facts in the knowledge base."""
        print(f"\n🔍 Searching for relationships: '{query}'")
        print("-" * 60)

        try:
            results = await self.graphiti.search(query)

            if not results:
                print("❌ No results found.")
                return

            print(f"✅ Found {len(results)} result(s):\n")

            for i, result in enumerate(results[:limit], 1):
                print(f"📄 Result {i}:")
                print(f"   Fact: {result.fact}")
                if hasattr(result, 'valid_at') and result.valid_at:
                    print(f"   Valid from: {result.valid_at}")
                if hasattr(result, 'invalid_at') and result.invalid_at:
                    print(f"   Valid until: {result.invalid_at}")
                print(f"   UUID: {result.uuid}")
                print()

        except Exception as e:
            print(f"❌ Error during search: {e}")

    async def search_nodes(self, query: str, limit: int = 5):
        """Search for nodes/entities in the knowledge base."""
        print(f"\n🎯 Searching for nodes: '{query}'")
        print("-" * 60)

        try:
            # Use node search configuration
            node_search_config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
            node_search_config.limit = limit

            results = await self.graphiti._search(
                query=query,
                config=node_search_config,
            )

            if not results.nodes:
                print("❌ No nodes found.")
                return

            print(f"✅ Found {len(results.nodes)} node(s):\n")

            for i, node in enumerate(results.nodes, 1):
                print(f"🏷️  Node {i}: {node.name}")
                summary = node.summary[:200] + "..." if len(node.summary) > 200 else node.summary
                print(f"   Summary: {summary}")
                print(f"   Labels: {', '.join(node.labels)}")
                print(f"   Created: {node.created_at}")
                if hasattr(node, 'attributes') and node.attributes:
                    print(f"   Attributes: {list(node.attributes.keys())}")
                print(f"   UUID: {node.uuid}")
                print()

        except Exception as e:
            print(f"❌ Error during node search: {e}")

    async def detailed_search(self, query: str, limit: int = 3):
        """Perform detailed search with reranking."""
        print(f"\n🔬 Detailed search: '{query}'")
        print("-" * 60)

        try:
            # First, get initial results
            initial_results = await self.graphiti.search(query)

            if not initial_results:
                print("❌ No results found.")
                return

            # Use the top result as center node for reranking
            center_node_uuid = initial_results[0].source_node_uuid
            print(f"🎯 Using center node: {center_node_uuid}")

            # Perform reranked search
            reranked_results = await self.graphiti.search(
                query, center_node_uuid=center_node_uuid
            )

            print(f"✅ Found {len(reranked_results)} reranked result(s):\n")

            for i, result in enumerate(reranked_results[:limit], 1):
                print(f"📊 Ranked Result {i}:")
                print(f"   Fact: {result.fact}")
                if hasattr(result, 'valid_at') and result.valid_at:
                    print(f"   Valid from: {result.valid_at}")
                if hasattr(result, 'invalid_at') and result.invalid_at:
                    print(f"   Valid until: {result.invalid_at}")
                print(f"   UUID: {result.uuid}")
                print()

        except Exception as e:
            print(f"❌ Error during detailed search: {e}")

    async def show_stats(self):
        """Show database statistics."""
        print("\n📊 DATABASE STATISTICS")
        print("-" * 60)

        try:
            # Try to get some basic stats by searching for common terms
            all_results = await self.graphiti.search("carbon OR emissions OR LNG OR regulation")

            print(f"📈 Total searchable facts: {len(all_results)}")

            # Try node search to get node count
            node_search_config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
            node_search_config.limit = 100

            node_results = await self.graphiti._search(
                query="carbon OR emissions OR LNG OR regulation",  # Use a real query instead of "*"
                config=node_search_config,
            )

            print(f"🏷️  Total nodes: {len(node_results.nodes)}")
            print(f"📅 Database path: {KUZU_DB_PATH}")
            print()

        except Exception as e:
            print(f"❌ Error getting stats: {e}")

    async def run_interactive(self):
        """Run the interactive CLI."""
        await self.initialize()

        try:
            self.print_banner()

            while True:
                try:
                    # Get user input
                    user_input = input("\n💬 Query> ").strip()

                    if not user_input:
                        continue

                    # Parse command
                    parts = user_input.split(' ', 1)
                    command = parts[0].lower()
                    query = parts[1] if len(parts) > 1 else ""

                    # Handle commands
                    if command in ['quit', 'exit', 'q']:
                        print("\n👋 Goodbye!")
                        break

                    elif command == 'help':
                        self.print_help()

                    elif command == 'search':
                        if not query:
                            print("❌ Please provide a search query. Example: search LNG emissions")
                        else:
                            await self.search_relationships(query)

                    elif command == 'nodes':
                        if not query:
                            print("❌ Please provide a search query. Example: nodes Rystad Energy")
                        else:
                            await self.search_nodes(query)

                    elif command == 'detailed':
                        if not query:
                            print("❌ Please provide a search query. Example: detailed marine LNG findings")
                        else:
                            await self.detailed_search(query)

                    elif command == 'stats':
                        await self.show_stats()

                    else:
                        # Default to relationship search
                        await self.search_relationships(user_input)

                except KeyboardInterrupt:
                    print("\n\n👋 Goodbye!")
                    break
                except EOFError:
                    print("\n\n👋 Goodbye!")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")

        finally:
            await self.close()


async def main():
    """Main function."""
    cli = ArticleQueryCLI()

    # Check if query provided as command line argument
    if len(sys.argv) > 1:
        # Non-interactive mode
        query = ' '.join(sys.argv[1:])
        await cli.initialize()
        try:
            await cli.search_relationships(query)
        finally:
            await cli.close()
    else:
        # Interactive mode
        await cli.run_interactive()


if __name__ == '__main__':
    asyncio.run(main())