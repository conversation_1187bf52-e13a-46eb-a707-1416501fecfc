# Carbon Regulation News Application Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# API KEYS (REQUIRED)
# =============================================================================

# Tavily API Key - Required for web search and content extraction
# Get your key at: https://tavily.com
TAVILY_API_KEY=your_tavily_api_key_here

# OpenRouter API Key - Required for AI processing
# Get your key at: https://openrouter.ai
OPENROUTER_API_KEY=your_openrouter_api_key_here

# =============================================================================
# OPTIONAL ENVIRONMENT OVERRIDES
# =============================================================================
# Most settings are configured in config.yaml
# These environment variables can override config.yaml settings if needed

# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=INFO
